{"cms_detection_rules": {"wordpress": {"name": "WordPress", "confidence_threshold": 0.5, "detection_methods": {"url_patterns": ["/wp-content/", "/wp-includes/", "/wp-admin/", "/wp-json/", "/?p=", "/feed/"], "dom_selectors": ["link[href*='wp-content']", "script[src*='wp-content']", "link[href*='wp-includes']", "script[src*='wp-includes']", ".wp-block-", "#wp-", ".widget", ".post-", ".page-id-"], "meta_generators": ["WordPress", "WordPress.com"], "headers": {"X-Powered-By": ["WordPress"], "Server": ["WordPress"]}, "cookies": ["wordpress_", "wp-", "comment_author_"]}, "comment_form": {"selectors": ["#commentform", ".comment-form", "form[action*='wp-comments-post.php']", "#respond form"], "fields": {"author": ["#author", "input[name='author']"], "email": ["#email", "input[name='email']"], "url": ["#url", "input[name='url']"], "comment": ["#comment", "textarea[name='comment']"], "submit": ["#submit", "input[type='submit'][name='submit']"]}}}, "drupal": {"name": "<PERSON><PERSON><PERSON>", "confidence_threshold": 0.5, "detection_methods": {"url_patterns": ["/sites/default/", "/modules/", "/themes/", "/core/", "?q=", "/node/"], "dom_selectors": ["script[src*='/sites/']", "link[href*='/sites/']", "script[src*='/modules/']", "link[href*='/modules/']", ".region-", ".block-", ".node-", ".field-"], "meta_generators": ["<PERSON><PERSON><PERSON>"], "headers": {"X-Drupal-Cache": ["HIT", "MISS"], "X-Generator": ["<PERSON><PERSON><PERSON>"]}, "cookies": ["SESS", "SSESS"]}, "comment_form": {"selectors": [".comment-form", "form[id*='comment']", "#comment-form"], "fields": {"name": ["input[name*='name']", "#edit-name"], "mail": ["input[name*='mail']", "#edit-mail"], "homepage": ["input[name*='homepage']", "#edit-homepage"], "comment": ["textarea[name*='comment']", "#edit-comment-body"]}}}, "joomla": {"name": "<PERSON><PERSON><PERSON>", "confidence_threshold": 0.5, "detection_methods": {"url_patterns": ["/administrator/", "/components/", "/modules/", "/plugins/", "/templates/", "option=com_"], "dom_selectors": ["script[src*='/media/jui/']", "link[href*='/media/jui/']", "script[src*='/media/system/']", ".moduletable", ".componentheading", ".contentheading"], "meta_generators": ["<PERSON><PERSON><PERSON>!", "<PERSON><PERSON><PERSON>"], "headers": {"X-Content-Encoded-By": ["<PERSON><PERSON><PERSON>!"]}}, "comment_form": {"selectors": ["form[name='commentform']", ".comment-form"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "comment": ["textarea[name='comment']"]}}}, "discuz": {"name": "Discuz!", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/static/", "/data/", "/source/", "mod=", "forum.php", "thread-"], "dom_selectors": ["script[src*='static/js/']", "link[href*='static/css/']", "#discuz_tips", ".pls", ".plc", ".xi2"], "meta_generators": ["Discuz!", "Discuz! X"], "cookies": ["discuz_", "saltkey"]}, "comment_form": {"selectors": ["#fastpostform", ".fastpost", "form[name='fastpost']"], "fields": {"message": ["textarea[name='message']", "#fastpostmessage"], "subject": ["input[name='subject']"]}}}, "phpbb": {"name": "phpBB", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/styles/", "/language/", "viewtopic.php", "viewforum.php", "posting.php"], "dom_selectors": ["script[src*='/styles/']", "link[href*='/styles/']", ".phpbb", ".postbody", ".postprofile"], "meta_generators": ["phpBB"]}, "comment_form": {"selectors": ["form[name='postform']", "#postform"], "fields": {"message": ["textarea[name='message']"], "subject": ["input[name='subject']"]}}}, "typecho": {"name": "Typecho", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/usr/", "/var/", "/admin/", "index.php/"], "dom_selectors": ["script[src*='/usr/']", "link[href*='/usr/']", ".typecho", ".post-", ".comment-"], "meta_generators": ["Typecho"]}, "comment_form": {"selectors": ["#comment-form", ".comment-form", "form[action*='comment']"], "fields": {"author": ["input[name='author']"], "mail": ["input[name='mail']"], "url": ["input[name='url']"], "text": ["textarea[name='text']"]}}}, "zblog": {"name": "Z-Blog", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/zb_system/", "/zb_users/", "/admin/", "post/"], "dom_selectors": ["script[src*='zb_system']", "link[href*='zb_system']", ".post", ".article"], "meta_generators": ["Z-Blog", "Z-BlogPHP"]}, "comment_form": {"selectors": ["#comment-form", ".comment-form"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "homepage": ["input[name='homepage']"], "content": ["textarea[name='content']"]}}}, "wix": {"name": "Wix", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": [".wixsite.com", "/wix/", "_wix", ".wix.com"], "dom_selectors": ["[data-wix-*]", ".wix-*", "#wix-*", "script[src*='wix']", "body[data-host*='wix.com']", "script[src*='wixstatic.com']", ".wixui-"], "meta_generators": ["Wix.com Website Builder"], "headers": {"X-Wix-*": ["*"]}, "cookies": ["wix", "XSRF-TOKEN"]}, "comment_form": {"selectors": ["form[data-hook='blog-comment-form']", "div[data-hook='comments-root'] form", ".wix-form", "form[data-hook*='comment']"], "fields": {"name": ["input[data-hook='comment-input-name']", "input[placeholder*='Name']", "input[data-hook*='name']"], "email": ["input[data-hook='comment-input-email']", "input[placeholder*='Email']", "input[data-hook*='email']"], "comment": ["textarea[data-hook='comment-input-text']", "textarea[placeholder*='Write a comment']", "textarea[data-hook*='comment']"], "submit": ["button[data-hook='comment-submit-button']", "button[type='submit']"]}}}, "squarespace": {"name": "Squarespace", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": [".squarespace.com", "/s/", "/config/", "squarespace-cdn.com"], "dom_selectors": [".sqs-*", "[data-sqs-*]", "script[src*='squarespace']", "body.squarespace-damask", "link[href*='squarespace.com']", "script[src*='squarespace.com']", "#page"], "meta_generators": ["Squarespace"], "headers": {}, "cookies": ["squarespace-", "crumb"]}, "comment_form": {"selectors": [".sqs-simple-form", ".comment-form", "form[data-form-id]", "form.comment-form-wrapper", ".comment-form-wrapper"], "fields": {"name": ["input[data-form-field='name']", "input[name='author<PERSON>ame']", "input#author-name", "input[placeholder*='Name']"], "email": ["input[data-form-field='email']", "input[name='authorEmail']", "input#author-email", "input[placeholder*='Email']"], "url": ["input[name='authorUrl']", "input#author-url", "input[placeholder*='Website']"], "comment": ["textarea[data-form-field='message']", "textarea.comment-form-textarea", "textarea[placeholder*='Comment']"], "submit": ["button[type='submit']", ".submit-button", "input[type='submit']"], "csrf": ["input[name='crumb']"]}}}, "webflow": {"name": "Webflow", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": [".webflow.io", "/webflow/", "webflow.com", "webflow-asset"], "dom_selectors": ["[data-w-*]", ".w-*", "script[src*='webflow']", ".webflow-*", "script[src*='webflow.com']", "link[href*='webflow.com']"], "meta_generators": ["Webflow"], "headers": {}, "cookies": []}, "comment_form": {"selectors": [".w-form", "[data-name='form']", ".webflow-form", "form[data-name]"], "fields": {"name": ["input[data-name='Name']", "input[name='Name']", "input[placeholder*='Name']"], "email": ["input[data-name='Email']", "input[name='<PERSON><PERSON>']", "input[type='email']", "input[placeholder*='Email']"], "comment": ["textarea[data-name='Message']", "textarea[name='Message']", "textarea[placeholder*='Message']"], "submit": ["button[type='submit']", "input[type='submit']", ".w-button"]}}}, "shopify": {"name": "Shopify", "confidence_threshold": 0.8, "detection_methods": {"url_patterns": [".myshopify.com", "/collections/", "/products/", "/blogs/", "/checkout", "/cart"], "dom_selectors": [".shopify-section", ".product-form", "#shopify-features", "script[src*='shopify']", "script[src*='cdn.shopify.com']", "link[href*='cdn.shopify.com']", "meta[property='og:type'][content='product']"], "meta_generators": ["Shopify"], "headers": {"X-Shopify-Shop": ["*"], "Server": ["Shopify"]}, "cookies": [], "global_variables": ["window.Shopify", "window.ShopifyAnalytics"]}, "comment_form": {"selectors": [".product-review-form", ".review-form", "form[action*='reviews']", "form[action*='/comments']", "#comment_form", ".comment-form"], "fields": {"name": ["input[name='review[author]']", "input[name='comment[author]']", "input[name='author']"], "email": ["input[name='review[email]']", "input[name='comment[email]']", "input[name='email']"], "rating": ["input[name='review[rating]']", ".rating-input", "select[name='rating']"], "comment": ["textarea[name='review[body]']", "textarea[name='comment[body]']", "textarea[name='body']"], "article_id": ["input[name='article_id']"], "submit": ["button[type='submit']", "input[type='submit']", ".btn-submit"]}}}, "magento": {"name": "Magento/Adobe Commerce", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/magento/", "/admin_*", "/customer/", "/catalog/", "/static/frontend/", "/media/catalog/"], "dom_selectors": [".magento", "script[src*='mage']", ".product-*", "script[type='text/x-magento-init']", "body[data-container='body']", "script[src*='mage/']", ".checkout-*", "#maincontent"], "meta_generators": ["Magento"], "headers": {"X-Magento-Vary": ["*"], "X-Magento-Cache-Debug": ["*"]}, "cookies": ["mage-", "form_key", "private_content_version", "PHPSESSID"]}, "comment_form": {"selectors": ["#review-form", ".product-review-form", "form[action*='review']", "form.review-form"], "fields": {"nickname": ["input[name='nickname']", "input#nickname_field"], "title": ["input[name='title']", "input#summary_field"], "rating": ["input[name='ratings']", ".rating-*", "select[name='ratings']"], "comment": ["textarea[name='detail']", "textarea#review_field"], "form_key": ["input[name='form_key']"], "submit": ["button[type='submit']", "input[type='submit']", ".action.submit"]}}}, "prestashop": {"name": "PrestaShop", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/prestashop/", "/admin*/", "/modules/", "/themes/", "/controllers/"], "dom_selectors": ["script[src*='prestashop']", ".prestashop", "#prestashop", ".ps-*", "script[src*='prestashop.com']", ".product-comments"], "meta_generators": ["PrestaShop"], "headers": {"X-Powered-By": ["PrestaShop"]}, "cookies": []}, "comment_form": {"selectors": ["#new-review-form", ".product-comment-form", "form[id*='review']", "form[action*='/product-reviews']"], "fields": {"name": ["input[name='customer_name']", "input[name='nickname']", "input[name='name']"], "title": ["input[name='title']"], "comment": ["textarea[name='content']", "textarea[name='comment']"], "rating": ["input[name='grade']", "select[name='grade']"], "submit": ["button[type='submit']", "input[type='submit']", ".btn-primary"]}}}, "opencart": {"name": "OpenCart", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/opencart/", "/admin/", "/catalog/", "/system/", "/image/"], "dom_selectors": ["script[src*='opencart']", ".opencart", "#opencart"], "meta_generators": ["OpenCart"], "headers": {}, "cookies": []}, "comment_form": {"selectors": ["#form-review", ".review-form"], "fields": {"name": ["input[name='name']"], "comment": ["textarea[name='text']"], "rating": ["input[name='rating']", "select[name='rating']"], "submit": ["button[type='submit']", "input[type='submit']", "#button-review"]}}}, "ghost": {"name": "Ghost", "confidence_threshold": 0.8, "detection_methods": {"url_patterns": ["/ghost/", "/content/", "/assets/", "/public/"], "dom_selectors": [".gh-*", "script[src*='ghost']", ".post-*", ".kg-*", "body.ghost-", "script[src*='/public/']", "meta[name='generator'][content*='Ghost']"], "meta_generators": ["Ghost"], "headers": {}, "cookies": ["ghost-members-ssr-token"], "global_variables": ["window.ghost"]}, "comment_form": {"selectors": [".comment-form", "#ghost-comments", ".gh-comments", ".ghost-portal-form", "form[data-ghost-comment-form]"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "website": ["input[name='website']"], "comment": ["textarea[name='comment']"], "submit": ["button[type='submit']", "input[type='submit']", ".gh-submit"]}}}, "craft": {"name": "Craft CMS", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/craft/", "/admin/", "/cpresources/"], "dom_selectors": ["script[src*='craft']", ".craft", "[data-craft-*]", ".craft-content"], "meta_generators": ["Craft CMS"], "headers": {"X-Craft-Version": ["*"]}, "cookies": []}, "comment_form": {"selectors": [".comment-form", "form[method='post']", "form[action*='/actions/comments/saveComment']"], "fields": {"name": ["input[name='fields[name]']", "input[name='name']"], "email": ["input[name='fields[email]']", "input[name='email']"], "comment": ["textarea[name='fields[comment]']", "textarea[name='comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "blogger": {"name": "Blogger/Blogspot", "confidence_threshold": 0.8, "detection_methods": {"url_patterns": [".blogger.com", ".blogspot.com", "/blogger/"], "dom_selectors": [".blogger-*", "script[src*='blogger']", ".blog-*", ".post-*"], "meta_generators": ["Blogger"], "headers": {}, "cookies": []}, "comment_form": {"selectors": ["#comment-form", ".comment-form", "form[name='comment']"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "comment": ["textarea[name='content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "typo3": {"name": "TYPO3", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/typo3/", "/fileadmin/", "/typo3conf/"], "dom_selectors": ["script[src*='/typo3/']", ".typo3", ".t3-page"], "meta_generators": ["TYPO3"], "headers": {"X-TYPO3-ID": ["*"]}, "cookies": []}, "comment_form": {"selectors": ["form[action*='/index.php']", ".tx-news-comment-form"], "fields": {"name": ["input[name='tx_news_pi1[comment][author]']"], "email": ["input[name='tx_news_pi1[comment][email]']"], "comment": ["textarea[name='tx_news_pi1[comment][comment]']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "umbraco": {"name": "Umbraco", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/umbraco/", "/config/"], "dom_selectors": ["script[src*='/umbraco/']", ".umb-control-group"], "meta_generators": ["Umbraco"], "headers": {"X-Umbraco-Version": ["*"]}, "cookies": []}, "comment_form": {"selectors": ["form[action*='/umbraco/']", ".umb-comment-form"], "fields": {"name": ["input[name='Name']"], "email": ["input[name='<PERSON><PERSON>']"], "comment": ["textarea[name='Comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "concrete5": {"name": "Concrete5/Concrete CMS", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/dashboard/", "/concrete/", "/boards/", ".conversation"], "dom_selectors": ["script[src*='/concrete/']", ".ccm-block-comments"], "meta_generators": ["Concrete5", "Concrete CMS"], "headers": {"X-Concrete5-Version": ["*"]}, "cookies": []}, "comment_form": {"selectors": ["form[action*='/index.php/comments/']", "form#comment-form"], "fields": {"name": ["input[name='author']"], "email": ["input[name='email']"], "comment": ["textarea[name='content']", "textarea[name='comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "strapi": {"name": "<PERSON><PERSON><PERSON>", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/admin/", "/api/"], "dom_selectors": ["script[src*='/admin/']", ".strapi-container"], "meta_generators": ["<PERSON><PERSON><PERSON>"], "headers": {"X-Strapi-Version": ["*"]}, "cookies": []}, "comment_form": {"selectors": ["form[action*='/api/comments']"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "comment": ["textarea[name='content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "b2evolution": {"name": "b2evolution", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/comment.php", "/post_comment"], "dom_selectors": [".b2evoComment"], "meta_generators": ["b2evolution"], "headers": {}, "cookies": []}, "comment_form": {"selectors": ["form[action*='comment.php']", "form[action*='post_comment']", ".b2evo-comment-form"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "url": ["input[name='url']"], "comment": ["textarea[name='comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "impresscms": {"name": "ImpressCMS", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/modules/comments/"], "dom_selectors": [".icms-comment"], "meta_generators": ["ImpressCMS"], "headers": {}, "cookies": []}, "comment_form": {"selectors": ["form[action*='/modules/comments/']", ".icms-comment-form"], "fields": {"name": ["input[name='poster_name']"], "email": ["input[name='poster_email']"], "comment": ["textarea[name='comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "grav": {"name": "Grav", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/user/", "/system/"], "dom_selectors": [".grav-comments"], "meta_generators": ["Grav"], "headers": {}, "cookies": []}, "comment_form": {"selectors": [".grav-comments form"], "fields": {"name": ["input[name='name']"], "email": ["input[name='email']"], "comment": ["textarea[name='text']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "dedecms": {"name": "DedeCMS (织梦CMS)", "confidence_threshold": 0.8, "detection_methods": {"url_patterns": ["/dede/", "/member/", "/plus/", "/uploads/", "/templets/", "/include/"], "dom_selectors": ["script[src*='dede']", ".dede", "#dede", "script[src*='dedeajax']", ".channel"], "meta_generators": ["DedeCms", "织梦CMS"], "headers": {"X-Powered-By": ["DedeCMS"]}, "cookies": ["DedeUserID", "DedeLoginTime", "menuitems"]}, "comment_form": {"selectors": ["#pinglun", ".saytext", "form[name='feedback']", "#plpost"], "fields": {"name": ["input[name='username']", "input[name='msg_name']"], "comment": ["textarea[name='msg']", "textarea[name='saytext']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "empirecms": {"name": "帝国CMS (EmpireCMS)", "confidence_threshold": 0.8, "detection_methods": {"url_patterns": ["/e/", "/d/file/", "/skin/", "/member/", "/search/", "/e/action/"], "dom_selectors": ["script[src*='/e/']", ".empire", "#empire", "script[src*='empirecms']"], "meta_generators": ["Empire CMS", "帝国CMS", "EmpireCMS"], "headers": {}, "cookies": ["empirecms_", "ecms_", "loginuserid"]}, "comment_form": {"selectors": ["#pinglun_form", ".pl_form", "form[name='form1']", "#saypl"], "fields": {"name": ["input[name='username']", "input[name='name']"], "comment": ["textarea[name='saytext']", "textarea[name='pltext']"], "article_id": ["input[name='id']", "input[name='classid']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "phpcms": {"name": "PHPCMS", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/phpcms/", "/statics/", "/uploadfile/", "/index.php?m=", "/api/"], "dom_selectors": ["script[src*='phpcms']", ".phpcms", "#phpcms", "script[src*='statics']"], "meta_generators": ["PHPCMS", "phpcms"], "headers": {}, "cookies": ["phpcms_", "userid_flash", "username_flash"]}, "comment_form": {"selectors": ["#comment_form", ".comment_form", "form[name='myform']"], "fields": {"name": ["input[name='username']", "input[name='name']"], "email": ["input[name='email']"], "comment": ["textarea[name='content']", "textarea[name='message']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "metinfo": {"name": "MetInfo (米拓CMS)", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/admin/", "/upload/", "/templates/", "/lang/", "/app/system/"], "dom_selectors": ["script[src*='metinfo']", ".metinfo", "#metinfo", ".met-*"], "meta_generators": ["MetInfo", "米拓CMS", "MetInfo Enterprise"], "headers": {}, "cookies": ["metinfo_", "met_member_auth"]}, "comment_form": {"selectors": ["#feedback_form", ".feedback_form", "form[name='feedback']"], "fields": {"name": ["input[name='name']", "input[name='feedback_name']"], "email": ["input[name='email']"], "phone": ["input[name='tel']", "input[name='phone']"], "comment": ["textarea[name='content']", "textarea[name='feedback_content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "thinkcmf": {"name": "ThinkCMF", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/public/", "/app/", "/themes/", "/data/"], "dom_selectors": ["script[src*='thinkcmf']", ".thinkcmf", "#thinkcmf"], "meta_generators": ["ThinkCMF", "thinkcmf"], "headers": {}, "cookies": ["thinkcmf_", "cmf_user_token"]}, "comment_form": {"selectors": [".comment-form", "#comment_form", "form[data-form]"], "fields": {"name": ["input[name='user_name']", "input[name='name']"], "email": ["input[name='user_email']"], "comment": ["textarea[name='content']", "textarea[name='comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "qibo": {"name": "齐博CMS (Qibo)", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/admin/", "/hack/", "/cache/", "/template/", "/member/"], "dom_selectors": ["script[src*='qibo']", ".qibo", "#qibo", "script[src*='hack']"], "meta_generators": ["Qibo CMS", "齐博CMS", "qibocms"], "headers": {}, "cookies": ["qibocms_", "qibo_"]}, "comment_form": {"selectors": ["#pl_form", ".pl_form", "form[name='myform']"], "fields": {"name": ["input[name='name']", "input[name='username']"], "comment": ["textarea[name='info']", "textarea[name='content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "powereasy": {"name": "动易CMS (PowerEasy)", "confidence_threshold": 0.7, "detection_methods": {"url_patterns": ["/admin/", "/user/", "/plus/", "/template/", "/PowerEasy/"], "dom_selectors": ["script[src*='powereasy']", ".powereasy", "#powereasy"], "meta_generators": ["PowerEasy", "动易CMS"], "headers": {}, "cookies": ["powereasy_", "pe_"]}, "comment_form": {"selectors": ["#comment_form", ".comment_form"], "fields": {"name": ["input[name='UserName']", "input[name='Name']"], "comment": ["textarea[name='Content']", "textarea[name='Comment']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "yidiancms": {"name": "易点CMS (YiDianCMS)", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/admin/", "/uploads/", "/template/", "/yidian/"], "dom_selectors": ["script[src*='yidian']", ".y<PERSON>", "#yidian"], "meta_generators": ["YiDianCMS", "易点CMS"], "headers": {}, "cookies": []}, "comment_form": {"selectors": [".comment-form", "#commentForm"], "fields": {"name": ["input[name='name']"], "comment": ["textarea[name='content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "siteserver": {"name": "SiteServer CMS", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/siteserver/", "/ss/", "/admin/", "/upload/"], "dom_selectors": ["script[src*='siteserver']", ".siteserver", "#siteserver"], "meta_generators": ["SiteServer CMS"], "headers": {}, "cookies": []}, "comment_form": {"selectors": [".ss-form", "#ss_form"], "fields": {"name": ["input[name='UserName']"], "comment": ["textarea[name='Content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "kesioncms": {"name": "KesionCMS (科汛CMS)", "confidence_threshold": 0.6, "detection_methods": {"url_patterns": ["/admin/", "/ks_inc/", "/template/", "/kesion/"], "dom_selectors": ["script[src*='kesion']", ".kesion", "#kesion"], "meta_generators": ["KesionCMS", "科汛CMS"], "headers": {}, "cookies": []}, "comment_form": {"selectors": ["#ks_form", ".ks_form"], "fields": {"name": ["input[name='UserName']"], "comment": ["textarea[name='Content']"], "submit": ["button[type='submit']", "input[type='submit']"]}}}, "generic": {"name": "Generic/Unknown", "confidence_threshold": 0.3, "detection_methods": {"url_patterns": [], "dom_selectors": ["form", ".comment", ".comments", "#comments"], "meta_generators": [], "headers": {}, "cookies": []}, "comment_form": {"selectors": ["form", ".comment-form", "#comment-form", ".comments form", "#comments form"], "fields": {"name": ["input[name*='name']", "input[placeholder*='name']", "input[placeholder*='Name']"], "email": ["input[name*='email']", "input[type='email']", "input[placeholder*='email']"], "comment": ["textarea", "textarea[name*='comment']", "textarea[placeholder*='comment']"], "submit": ["input[type='submit']", "button[type='submit']", ".submit"]}}}}, "detection_config": {"timeout": 10, "max_redirects": 5, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"], "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.5", "Accept-Encoding": "gzip, deflate", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1"}}}