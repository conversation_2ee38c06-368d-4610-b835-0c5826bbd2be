#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将评论机器人打包成exe文件
优化版本：自动清理打包文件，只保留exe
"""

import sys
import subprocess
import shutil
import time
from pathlib import Path

def install_requirements():
    """安装依赖"""
    print("正在安装依赖...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

def clean_old_build():
    """清理旧的打包文件"""
    print("🧹 清理旧的打包文件...")

    # 删除旧的build和dist目录
    for dir_name in ["build", "dist", "__pycache__"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")

    # 删除.spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"   删除文件: {spec_file}")

def build_exe():
    """打包成exe"""
    print("📦 正在打包成exe文件...")

    # 获取当前时间戳用于版本标识
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    exe_name = f"CommentBot_v{timestamp}"

    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 不显示控制台窗口
        f"--name={exe_name}",           # exe文件名（带时间戳）
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        "--add-data=ai_service.py;.",   # 包含AI服务
        "--add-data=comment.py;.",      # 包含comment.py
        "--hidden-import=PyQt5.QtCore", # 确保PyQt5核心被包含
        "--hidden-import=PyQt5.QtWidgets", # 确保PyQt5组件被包含
        "--hidden-import=PyQt5.QtGui",  # 确保PyQt5 GUI被包含
        "--hidden-import=requests",     # 确保requests被包含
        "--hidden-import=urllib3",      # 确保urllib3被包含
        "--hidden-import=bs4",          # 确保BeautifulSoup被包含
        "--hidden-import=json",         # 确保json被包含
        "--hidden-import=threading",    # 确保threading被包含
        "comment_gui.py"                # 主文件
    ]

    # 如果有图标文件，添加图标参数
    if Path("logo.ico").exists():
        cmd.insert(-1, "--icon=logo.ico")

    try:
        print("   执行PyInstaller...")
        subprocess.run(cmd, check=True)
        print("✅ 打包完成！")
        return exe_name
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return None
    except FileNotFoundError:
        print("❌ 未找到pyinstaller，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("请重新运行打包脚本")
        return None

def cleanup_build_files(exe_name):
    """清理打包过程中生成的文件，只保留exe"""
    print("🧹 清理打包文件...")

    exe_path = Path("dist") / f"{exe_name}.exe"

    if exe_path.exists():
        # 将exe文件移动到当前目录
        final_exe_path = Path(f"{exe_name}.exe")
        if final_exe_path.exists():
            final_exe_path.unlink()  # 删除旧的exe文件

        shutil.move(str(exe_path), str(final_exe_path))
        print(f"✅ exe文件已移动到: {final_exe_path}")

        # 删除build和dist目录
        for dir_name in ["build", "dist"]:
            if Path(dir_name).exists():
                shutil.rmtree(dir_name)
                print(f"   删除目录: {dir_name}")

        # 删除.spec文件
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
            print(f"   删除文件: {spec_file}")

        return final_exe_path
    else:
        print("❌ 未找到生成的exe文件")
        return None

def create_icon():
    """检查图标文件"""
    if Path("logo.ico").exists():
        print("✅ 找到图标文件: logo.ico")
    else:
        print("⚠️ 未找到图标文件: logo.ico，将使用默认图标")

def get_version_info():
    """获取版本信息"""
    try:
        # 尝试从git获取版本信息
        result = subprocess.run(
            ["git", "describe", "--tags", "--always"],
            capture_output=True, text=True, check=True
        )
        return result.stdout.strip()
    except:
        return "v1.0.0"

def main():
    print("🚀 评论机器人打包工具 (优化版)")
    print("=" * 60)

    # 显示版本信息
    version = get_version_info()
    print(f"📋 当前版本: {version}")
    print(f"🕒 打包时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 检查必要文件
    required_files = ["comment_gui.py", "comment.py", "ai_service.py", "requirements.txt"]
    print("🔍 检查必要文件...")
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return
        else:
            print(f"✅ 找到文件: {file}")

    # 清理旧的打包文件
    clean_old_build()

    # 安装依赖
    install_requirements()

    # 创建图标提示
    create_icon()

    # 打包
    exe_name = build_exe()

    if exe_name:
        # 清理打包文件
        final_exe_path = cleanup_build_files(exe_name)

        if final_exe_path:
            file_size = final_exe_path.stat().st_size / (1024 * 1024)  # MB

            print("\n" + "=" * 60)
            print("🎉 打包完成！")
            print("=" * 60)
            print(f"📁 生成的文件: {final_exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            print(f"🏷️  版本标识: {version}")
            print()
            print("📖 使用说明:")
            print("1. 将exe文件复制到任意目录")
            print("2. 运行程序，会自动生成配置文件和数据文件")
            print("3. 在'内容管理'标签页中添加URL、评论和用户信息")
            print("4. 在'控制台'标签页中开始运行")
            print("5. 新增AI生成功能，可在评论管理中使用")
            print()
            print("🔧 新功能:")
            print("- AI评论生成 (支持3种模型)")
            print("- 智能锚文本链接插入")
            print("- 异步处理，界面不卡顿")
            print("- 自动去重功能")
            print("- 优化的用户界面")
        else:
            print("❌ 打包过程中出现错误")
    else:
        print("❌ 打包失败")

if __name__ == "__main__":
    main()
