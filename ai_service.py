#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI评论生成服务
调用硅基流动API生成英文评论
"""

import requests
import json
import os
import urllib3
import ssl
from typing import List, Dict, Optional
from pathlib import Path

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class AICommentGenerator:
    """AI评论生成器"""

    def __init__(self, config=None):
        # 加载配置
        self.config = self._load_config(config)

        # 从配置获取API信息
        ai_config = self.config.get('ai_service', {})
        self.api_url = ai_config.get('api_url', "https://api.siliconflow.cn/v1/chat/completions")
        self.api_key = ai_config.get('api_key', "")

        # 验证API密钥
        if not self.api_key:
            print("⚠️ 警告: 未配置AI服务API密钥，请在配置中设置")

        # 初始化模型配置
        self._init_models()

    def _load_config(self, config=None):
        """加载配置文件"""
        if config is not None:
            return config

        # 尝试从config.json加载
        config_file = 'config.json'
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return {}

        return {}

    def _init_models(self):
        """初始化模型配置"""
        # 模型配置
        self.models = {
            "Qwen/Qwen3-8B": {
                "name": "Qwen/Qwen3-8B",
                "system_prompt_support": True,
                "max_tokens": 8192,
                "temperature": 0.6,
                "top_p": 0.95,
                "top_k": 20,
                "min_p": 0.00,
                "frequency_penalty": 0.0,
                "enable_thinking": True,
                "thinking_budget": 4096
            },
            "THUDM/GLM-4.1V-9B-Thinking": {
                "name": "THUDM/GLM-4.1V-9B-Thinking",
                "system_prompt_support": False,
                "max_tokens": 8192,
                "temperature": 0.95,
                "top_p": 0.7,
                "top_k": 2,
                "repetition_penalty": 1.1,
                "thinking_budget": 8192
            },
            "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {
                "name": "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                "system_prompt_support": True,
                "max_tokens": 8192,
                "temperature": 0.6,
                "top_p": 0.95,
                "top_k": 20,
                "frequency_penalty": 0.0,
                "thinking_budget": 4096
            }
        }
        
        # System Prompt
        self.system_prompt = """You are a thoughtful and articulate reader who leaves meaningful, natural, and respectful comments under blog posts. Your comments are context-aware, insightful, and written in fluent English. Avoid vague praise or robotic expressions. Vary your opening lines to make each comment unique and human-like."""
        
        # User Prompt
        self.user_prompt = """You are a thoughtful English-speaking reader who comments under various types of blog posts. Generate 10 unique, general-purpose English comments that could reasonably fit under different kinds of blog articles (e.g., tutorials, news, opinion pieces, personal stories, etc.). Each comment should be 2 to 4 sentences long, begin with a different opening, and sound human-written. Avoid vague or robotic phrases, and ensure the tone is sincere, natural, and relevant across broad topics.

Output the result strictly as a JSON array of 10 strings, no explanations, no extra text.

Respond in this exact format:
[
  "Comment 1",
  "Comment 2",
  "Comment 3",
  ...
  "Comment 10"
]"""
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return list(self.models.keys())
    
    def get_model_config(self, model_name: str) -> Dict:
        """获取模型配置"""
        return self.models.get(model_name, self.models["Qwen/Qwen3-8B"])
    
    def generate_comments(self, model_name: str = "Qwen/Qwen3-8B", count: int = 10, 
                         custom_params: Optional[Dict] = None) -> Dict:
        """
        生成评论
        
        Args:
            model_name: 模型名称
            count: 生成数量 (1-10)
            custom_params: 自定义参数
            
        Returns:
            Dict: {"success": bool, "comments": List[str], "error": str}
        """
        try:
            # 获取模型配置
            model_config = self.get_model_config(model_name)
            
            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 构建消息
            messages = []
            
            # 添加system prompt（如果模型支持）
            if model_config["system_prompt_support"]:
                messages.append({
                    "role": "system",
                    "content": self.system_prompt
                })
            
            # 修改user prompt中的数量
            user_prompt = self.user_prompt.replace("Generate 10 unique", f"Generate {count} unique")
            user_prompt = user_prompt.replace("Comment 10", f"Comment {count}")
            
            messages.append({
                "role": "user",
                "content": user_prompt
            })
            
            # 构建请求数据
            data = {
                "model": model_config["name"],
                "messages": messages,
                "max_tokens": model_config["max_tokens"],
                "temperature": model_config["temperature"],
                "top_p": model_config["top_p"],
                "top_k": model_config["top_k"]
            }
            
            # 添加模型特定参数
            if "frequency_penalty" in model_config:
                data["frequency_penalty"] = model_config["frequency_penalty"]
            if "repetition_penalty" in model_config:
                data["repetition_penalty"] = model_config["repetition_penalty"]
            if "thinking_budget" in model_config:
                data["thinking_budget"] = model_config["thinking_budget"]
            if "min_p" in model_config:
                data["min_p"] = model_config["min_p"]
            
            # 应用自定义参数
            if custom_params:
                data.update(custom_params)
            
            # 创建一个新的Session，完全绕过代理
            session = requests.Session()

            # 清除所有代理设置
            session.proxies.clear()
            session.trust_env = False  # 不信任环境变量

            # 临时清除环境变量
            env_backup = {}
            proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
            for var in proxy_vars:
                if var in os.environ:
                    env_backup[var] = os.environ[var]
                    del os.environ[var]

            try:
                # 发送请求（完全绕过代理）
                response = session.post(
                    self.api_url,
                    headers=headers,
                    json=data,
                    proxies=None,  # 完全禁用代理
                    timeout=60,
                    verify=True
                )
            finally:
                # 恢复环境变量
                for var, value in env_backup.items():
                    os.environ[var] = value
                session.close()
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                # 解析JSON格式的评论
                try:
                    comments = json.loads(content)
                    if isinstance(comments, list):
                        return {
                            "success": True,
                            "comments": comments[:count],  # 确保数量正确
                            "error": None
                        }
                    else:
                        return {
                            "success": False,
                            "comments": [],
                            "error": "AI返回的不是列表格式"
                        }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试按行分割
                    lines = [line.strip() for line in content.split('\n') if line.strip()]
                    comments = [line for line in lines if not line.startswith('[') and not line.startswith(']')]
                    return {
                        "success": True,
                        "comments": comments[:count],
                        "error": None
                    }
            else:
                return {
                    "success": False,
                    "comments": [],
                    "error": f"API请求失败: {response.status_code} - {response.text}"
                }
                
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "comments": [],
                "error": "请求超时，请检查网络连接"
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "comments": [],
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "comments": [],
                "error": f"生成评论时出错: {str(e)}"
            }

# 测试函数
if __name__ == "__main__":
    generator = AICommentGenerator()
    
    print("🤖 测试AI评论生成...")
    result = generator.generate_comments("Qwen/Qwen3-8B", 3)
    
    if result["success"]:
        print("✅ 生成成功！")
        for i, comment in enumerate(result["comments"], 1):
            print(f"{i}. {comment}")
    else:
        print(f"❌ 生成失败: {result['error']}")
