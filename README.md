# 评论机器人 GUI版本

一个基于PyQt5的自动评论机器人，支持多种CMS系统的评论表单识别和自动提交。

## 功能特点

- 🎯 **多CMS支持**: 支持WordPress、Dr<PERSON><PERSON>、Joomla等主流CMS
- 🚀 **多线程处理**: 5线程并行处理，提高效率
- 🔧 **图形界面**: 直观的GUI界面，易于使用
- 📊 **实时监控**: 实时显示处理进度和统计信息
- ⚙️ **灵活配置**: 支持代理设置、性能调优等
- 📝 **内容管理**: 方便的URL、评论、用户信息管理

## 安装和运行

### 方法1: 直接运行源码

1. 安装Python 3.7+
2. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```
3. 运行程序:
   ```bash
   python comment_gui.py
   ```

### 方法2: 打包成exe文件

1. 运行打包脚本:
   ```bash
   python build.py
   ```
2. 在`dist`目录找到`CommentBot.exe`
3. 双击运行

## 使用说明

### 1. 配置设置
- **代理设置**: 默认使用HTTP代理127.0.0.1:7897
- **性能设置**: 可调整线程数、超时时间、延迟范围
- **其他设置**: SSL验证、日志级别等

### 2. 内容管理
- **URL管理**: 添加要处理的网站URL
- **评论管理**: 添加随机评论内容
- **用户管理**: 添加用户信息（姓名|邮箱|网站）

### 3. 运行控制
- **开始**: 开始处理所有URL
- **暂停/继续**: 暂停或继续处理
- **停止**: 停止所有处理
- **实时日志**: 查看处理过程和结果

## 文件说明

- `comment_gui.py`: GUI主程序
- `comment.py`: 核心评论机器人逻辑
- `config.json`: 配置文件（自动生成）
- `url.txt`: URL列表
- `comment.txt`: 评论内容列表
- `website.txt`: 用户信息列表
- `success.txt`: 成功记录
- `error.txt`: 失败记录

## 支持的CMS系统

### 国外主流CMS
- WordPress (最佳支持)
- Drupal
- Joomla
- Ghost
- Blogger
- Squarespace
- Magento
- 等等...

### 国内主流CMS
- 织梦CMS (DedeCMS)
- 帝国CMS (EmpireCMS)
- PHPCMS
- Discuz!
- Z-Blog
- 等等...

## 注意事项

1. **代理设置**: 确保代理服务器正常运行
2. **网站政策**: 遵守目标网站的使用条款
3. **频率控制**: 合理设置延迟时间，避免被封IP
4. **内容质量**: 使用有意义的评论内容
5. **法律合规**: 确保使用符合当地法律法规

## 技术特性

- **智能表单识别**: 自动识别各种CMS的评论表单
- **字段智能填充**: 自动匹配不同的字段名称
- **错误处理**: 完善的错误处理和重试机制
- **线程安全**: 多线程环境下的文件操作安全
- **配置持久化**: 设置自动保存和加载

## 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理服务器是否运行
   - 确认代理地址和端口正确

2. **无法找到评论表单**
   - 网站可能使用第三方评论系统（如Disqus）
   - 网站可能需要登录才能评论

3. **评论提交失败**
   - 网站可能有反垃圾机制
   - 可能需要验证码验证

4. **程序运行缓慢**
   - 调整线程数量
   - 减少延迟时间
   - 检查网络连接

## 更新日志

### v1.2
- 初始版本发布
- 基本的GUI界面
- 多线程处理支持
- 配置管理功能

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途或违法活动。
