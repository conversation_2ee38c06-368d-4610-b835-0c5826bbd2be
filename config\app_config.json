{"app": {"name": "Comme<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "language": "zh-CN", "debug_mode": false, "auto_update": true, "telemetry_enabled": false}, "processing": {"max_concurrent_requests": 3, "request_timeout": 30, "retry_attempts": 3, "delay_between_requests": [2, 5], "user_agent_rotation": true, "respect_robots_txt": true, "max_redirects": 5}, "paths": {"cms_rules_dir": "cms_rules/", "data_dir": "data/", "templates_dir": "templates/", "logs_dir": "logs/", "backups_dir": "backups/"}, "features": {"auto_cms_detection": true, "article_discovery": true, "comment_generation": true, "link_insertion": true, "success_tracking": true, "duplicate_detection": true, "content_analysis": true}, "security": {"verify_ssl": true, "use_proxy": false, "proxy_rotation": false, "rate_limiting": true, "max_requests_per_minute": 30}, "proxy": {"enabled": true, "http_proxy": "http://127.0.0.1:7897", "https_proxy": "http://127.0.0.1:7897", "no_proxy": ["127.0.0.1", "localhost", "api.siliconflow.cn", "*.siliconflow.cn"], "timeout": 30, "verify_ssl": true, "use_for_domestic_api": false}, "task_scheduler": {"max_workers": 5, "queue_size": 100, "task_timeout": 300, "cleanup_interval": 3600, "auto_retry_failed": true, "max_retry_attempts": 3}}