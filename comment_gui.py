#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评论机器人 GUI界面
基于PyQt5的图形用户界面
"""

import warnings
# 忽略PyQt5的sipPyTypeDict警告
warnings.filterwarnings("ignore", message="sipPyTypeDict.*deprecated")

import sys
import os
import json
import threading
import time
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from comment import CommentBot

class AIGenerateThread(QThread):
    """AI生成评论线程"""
    progress_signal = pyqtSignal(str)  # 进度信息信号
    result_signal = pyqtSignal(dict)   # 结果信号
    error_signal = pyqtSignal(str)     # 错误信号

    def __init__(self, generator, model_name, count, custom_params):
        super().__init__()
        self.generator = generator
        self.model_name = model_name
        self.count = count
        self.custom_params = custom_params

    def run(self):
        """执行AI生成"""
        try:
            self.progress_signal.emit("正在连接AI服务...")

            # 模拟连接延迟
            self.msleep(500)

            self.progress_signal.emit("正在生成评论，请稍等2-3分钟...")

            # 调用AI生成
            result = self.generator.generate_comments(
                self.model_name,
                self.count,
                self.custom_params
            )

            self.progress_signal.emit("生成完成！")
            self.result_signal.emit(result)

        except Exception as e:
            self.error_signal.emit(f"生成评论时出错: {str(e)}")

class ConfigManager:
    """配置管理器"""
    def __init__(self):
        self.config_file = 'config.json'
        self.default_config = {
            'proxy': {
                'type': 'HTTP',
                'host': '127.0.0.1',
                'port': 7897,
                'enabled': True
            },
            'performance': {
                'threads': 5,
                'timeout': 20,
                'delay_min': 0.5,
                'delay_max': 1.5
            },
            'link_insertion': {
                'enabled': True,
                'insertion_rate': 0.7,
                'format': 'html',
                'position': 'end'
            },
            'domain_filtering': {
                'failure_threshold': 10,
                'enable_directory_detection': True,
                'enable_blocked_domain_filter': True,
                'auto_cleanup_blocked_domains': True,
                'reset_domain_stats_on_start': True
            },
            'ai_service': {
                'api_url': 'https://api.siliconflow.cn/v1/chat/completions',
                'api_key': '',
                'default_model': 'Qwen/Qwen3-8B',
                'timeout': 30,
                'max_retries': 3
            },
            'other': {
                'ssl_verify': False,
                'log_level': 'INFO'
            }
        }
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        if Path(self.config_file).exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except:
                self.config = self.default_config.copy()
        else:
            self.config = self.default_config.copy()
            self.save_config()
    
    def save_config(self):
        """保存配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def get(self, section, key):
        """获取配置值"""
        return self.config.get(section, {}).get(key)
    
    def set(self, section, key, value):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self.save_config()

class FileManager:
    """文件管理器"""
    def __init__(self):
        self.files = {
            'url.txt': [],
            'comment.txt': [],
            'website.txt': [],
            'success.txt': [],
            'error.txt': []
        }
        self.ensure_files_exist()
        self.load_all_files()
    
    def ensure_files_exist(self):
        """确保文件存在"""
        default_contents = {
            'url.txt': [
                'https://example1.com/',
                'https://example2.com/',
                'https://example3.com/'
            ],
            'comment.txt': [
                'Great article!',
                'Thanks for sharing.',
                'Very informative.',
                'Learned a lot from this.',
                'Excellent content!'
            ],
            'website.txt': [
                'TechReviewer----https://example1.com',
                'DigitalExpert----https://example2.com',
                'WebMaster----https://example3.com'
            ],
            'success.txt': [],
            'error.txt': []
        }
        
        for filename, default_content in default_contents.items():
            if not Path(filename).exists():
                with open(filename, 'w', encoding='utf-8') as f:
                    for line in default_content:
                        f.write(f"{line}\n")
    
    def load_all_files(self):
        """加载所有文件"""
        for filename in self.files.keys():
            self.load_file(filename)
    
    def load_file(self, filename):
        """加载单个文件"""
        if Path(filename).exists():
            with open(filename, 'r', encoding='utf-8') as f:
                self.files[filename] = [line.strip() for line in f if line.strip()]
        else:
            self.files[filename] = []
    
    def save_file(self, filename, content):
        """保存文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for line in content:
                f.write(f"{line}\n")
        self.files[filename] = content.copy()
    
    def get_file_content(self, filename):
        """获取文件内容"""
        return self.files.get(filename, [])

class WorkerThread(QThread):
    """工作线程"""
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # current, total
    finished_signal = pyqtSignal()
    success_signal = pyqtSignal(str)  # 成功信号
    error_signal = pyqtSignal(str)    # 失败信号
    stats_update_signal = pyqtSignal()  # 统计更新信号
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.running = False
    
    def run(self):
        """运行评论机器人"""
        self.running = True
        self.log_signal.emit("🚀 评论机器人启动...")

        try:
            # 创建CommentBot实例（不进行代理测试）
            from comment import CommentBot

            # 从配置管理器获取设置
            config = {
                'proxy_host': self.config_manager.get('proxy', 'host'),
                'proxy_port': self.config_manager.get('proxy', 'port'),
                'timeout': self.config_manager.get('performance', 'timeout'),
                'delay_min': self.config_manager.get('performance', 'delay_min'),
                'delay_max': self.config_manager.get('performance', 'delay_max'),
                'link_insertion': {
                    'enabled': self.config_manager.get('link_insertion', 'enabled'),
                    'insertion_rate': self.config_manager.get('link_insertion', 'insertion_rate'),
                    'format': self.config_manager.get('link_insertion', 'format'),
                    'position': self.config_manager.get('link_insertion', 'position')
                }
            }

            bot = CommentBot(test_proxy=False, config=config)

            # 检查并去重URL
            self.log_signal.emit("📋 检查URL重复...")
            bot.deduplicate_urls()

            # 获取待处理URL
            pending_urls = bot.get_all_pending_urls()
            total = len(pending_urls)

            if total == 0:
                self.log_signal.emit("✅ 所有URL已处理完成！")
                self.finished_signal.emit()
                return

            self.log_signal.emit(f"待处理URL数量: {total}")

            # 初始化进度
            self.progress_signal.emit(0, total)
            processed_count = 0

            # 使用配置的线程数量并行处理
            from concurrent.futures import ThreadPoolExecutor
            import threading

            # 从配置获取线程数量
            thread_count = self.config_manager.get('performance', 'threads') or 5

            # 线程安全的计数器
            progress_lock = threading.Lock()

            def process_single_url_wrapper(url):
                nonlocal processed_count
                if not self.running:
                    return

                self.log_signal.emit(f"处理URL: {url}")

                # 处理单个URL并获取结果
                result = self.process_url_with_feedback(bot, url)

                if result['success']:
                    self.success_signal.emit(result['article_url'])
                    self.log_signal.emit(f"✅ 成功: {url}")
                else:
                    self.error_signal.emit(url)
                    self.log_signal.emit(f"❌ 失败: {url} - {result['reason']}")

                # 更新进度（线程安全）
                with progress_lock:
                    processed_count += 1
                    self.progress_signal.emit(processed_count, total)

                # 更新统计
                self.stats_update_signal.emit()

            # 使用配置的线程数量并行处理
            self.log_signal.emit(f"使用 {thread_count} 个线程并行处理")
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                executor.map(process_single_url_wrapper, pending_urls)

            self.log_signal.emit("✅ 所有任务完成！")
            
        except Exception as e:
            self.log_signal.emit(f"❌ 运行出错: {e}")
        
        finally:
            self.running = False
            self.finished_signal.emit()
    
    def stop(self):
        """停止运行"""
        self.running = False

    def process_url_with_feedback(self, bot, url):
        """处理单个URL并返回详细结果"""
        try:
            # 查找文章
            article_url = bot.find_latest_article(url)
            if not article_url:
                bot.save_error(url)
                return {'success': False, 'reason': '未找到文章', 'article_url': None}

            # 查找评论表单
            form, article_url = bot.find_comment_form(article_url)
            if not form:
                bot.save_error(url)
                return {'success': False, 'reason': '未找到评论表单', 'article_url': None}

            # 获取评论内容
            comment_text = bot.get_random_comment()
            if not comment_text:
                return {'success': False, 'reason': '未获取到评论内容', 'article_url': None}

            # 获取用户信息
            user_info, _ = bot.get_random_website_info()
            if not user_info:
                return {'success': False, 'reason': '未获取到用户信息', 'article_url': None}

            # 提交评论
            success = bot.submit_comment(form, article_url, comment_text, user_info)

            if success:
                bot.save_success(article_url)
                return {'success': True, 'reason': '评论提交成功', 'article_url': article_url}
            else:
                bot.save_error(url)
                return {'success': False, 'reason': '评论提交失败', 'article_url': None}

        except Exception as e:
            bot.save_error(url)
            return {'success': False, 'reason': f'处理异常: {str(e)}', 'article_url': None}

class DashboardTab(QWidget):
    """控制台标签页"""
    def __init__(self, config_manager, file_manager):
        super().__init__()
        self.config_manager = config_manager
        self.file_manager = file_manager
        self.worker = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 状态区域
        status_group = QGroupBox("运行状态")
        status_layout = QVBoxLayout()
        
        self.status_label = QLabel("状态: 停止")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        # 设置绿色进度条样式
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.progress_bar)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 统计区域
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout()
        
        self.success_label = QLabel("成功: 0")
        self.error_label = QLabel("失败: 0")
        self.total_label = QLabel("总数: 0")
        
        stats_layout.addWidget(self.success_label, 0, 0)
        stats_layout.addWidget(self.error_label, 0, 1)
        stats_layout.addWidget(self.total_label, 0, 2)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # 控制按钮
        control_group = QGroupBox("控制")
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始")
        self.stop_btn = QPushButton("停止")
        self.clear_log_btn = QPushButton("清空日志")

        self.start_btn.clicked.connect(self.start_task)
        self.stop_btn.clicked.connect(self.stop_task)
        self.clear_log_btn.clicked.connect(self.clear_log)

        self.stop_btn.setEnabled(False)

        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.clear_log_btn)
        
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # 日志区域
        log_group = QGroupBox("实时日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(300)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        self.setLayout(layout)
        self.update_stats()
    
    def update_stats(self):
        """更新统计信息"""
        # 重新加载文件内容
        self.file_manager.load_file('success.txt')
        self.file_manager.load_file('error.txt')
        self.file_manager.load_file('url.txt')

        success_count = len(self.file_manager.get_file_content('success.txt'))
        error_count = len(self.file_manager.get_file_content('error.txt'))
        total_count = len(self.file_manager.get_file_content('url.txt'))

        self.success_label.setText(f"成功: {success_count}")
        self.success_label.setStyleSheet("color: green; font-weight: bold;")

        self.error_label.setText(f"失败: {error_count}")
        self.error_label.setStyleSheet("color: red; font-weight: bold;")

        self.total_label.setText(f"总数: {total_count}")
        self.total_label.setStyleSheet("color: blue; font-weight: bold;")
    
    def start_task(self):
        """开始任务"""
        if self.worker and self.worker.isRunning():
            return
        
        self.worker = WorkerThread(self.config_manager)
        self.worker.log_signal.connect(self.add_log)
        self.worker.progress_signal.connect(self.update_progress)
        self.worker.finished_signal.connect(self.task_finished)
        self.worker.success_signal.connect(self.on_success)
        self.worker.error_signal.connect(self.on_error)
        self.worker.stats_update_signal.connect(self.update_stats)
        
        self.worker.start()
        
        self.status_label.setText("状态: 运行中")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: green;")
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
    
    def stop_task(self):
        """停止任务"""
        if self.worker:
            self.worker.stop()
            self.worker.wait()
        self.task_finished()
    
    def task_finished(self):
        """任务完成"""
        self.status_label.setText("状态: 停止")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: red;")
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.update_stats()
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def update_progress(self, current, total):
        """更新进度"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
    
    def clear_log(self):
        """清空日志和记录文件"""
        # 清空界面日志
        self.log_text.clear()

        # 清空success.txt和error.txt文件
        try:
            # 清空成功记录
            with open('success.txt', 'w', encoding='utf-8') as f:
                f.write('')

            # 清空错误记录
            with open('error.txt', 'w', encoding='utf-8') as f:
                f.write('')

            # 更新文件管理器
            self.file_manager.files['success.txt'] = []
            self.file_manager.files['error.txt'] = []

            # 更新统计信息
            self.update_stats()

            self.add_log("✅ 已清空所有日志和记录文件")

        except Exception as e:
            self.add_log(f"❌ 清空文件失败: {e}")

    def on_success(self, article_url):
        """处理成功信号"""
        # 重新加载文件内容
        self.file_manager.load_file('success.txt')
        # 不需要额外日志，因为WorkerThread已经发送了

    def on_error(self, url):
        """处理失败信号"""
        # 重新加载文件内容
        self.file_manager.load_file('error.txt')
        # 不需要额外日志，因为WorkerThread已经发送了

class SettingsTab(QWidget):
    """配置设置标签页"""
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 代理设置
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QGridLayout()

        proxy_layout.addWidget(QLabel("代理类型:"), 0, 0)
        self.proxy_type = QComboBox()
        self.proxy_type.addItems(["HTTP", "SOCKS5", "系统代理"])
        self.proxy_type.setCurrentText(self.config_manager.get('proxy', 'type'))
        proxy_layout.addWidget(self.proxy_type, 0, 1)

        proxy_layout.addWidget(QLabel("代理地址:"), 1, 0)
        self.proxy_host = QLineEdit(self.config_manager.get('proxy', 'host'))
        proxy_layout.addWidget(self.proxy_host, 1, 1)

        proxy_layout.addWidget(QLabel("端口:"), 2, 0)
        self.proxy_port = QSpinBox()
        self.proxy_port.setRange(1, 65535)
        self.proxy_port.setValue(self.config_manager.get('proxy', 'port'))
        proxy_layout.addWidget(self.proxy_port, 2, 1)

        self.proxy_enabled = QCheckBox("启用代理")
        self.proxy_enabled.setChecked(self.config_manager.get('proxy', 'enabled'))
        proxy_layout.addWidget(self.proxy_enabled, 3, 0, 1, 2)

        test_proxy_btn = QPushButton("测试代理")
        test_proxy_btn.clicked.connect(self.test_proxy)
        proxy_layout.addWidget(test_proxy_btn, 4, 0, 1, 2)

        proxy_group.setLayout(proxy_layout)
        layout.addWidget(proxy_group)

        # 性能设置
        perf_group = QGroupBox("性能设置")
        perf_layout = QGridLayout()

        perf_layout.addWidget(QLabel("线程数量:"), 0, 0)
        self.threads = QSpinBox()
        self.threads.setRange(1, 10)
        self.threads.setValue(self.config_manager.get('performance', 'threads'))
        perf_layout.addWidget(self.threads, 0, 1)

        perf_layout.addWidget(QLabel("超时时间(秒):"), 1, 0)
        self.timeout = QSpinBox()
        self.timeout.setRange(5, 120)
        self.timeout.setValue(self.config_manager.get('performance', 'timeout'))
        perf_layout.addWidget(self.timeout, 1, 1)

        perf_layout.addWidget(QLabel("延迟范围(秒):"), 2, 0)
        delay_layout = QHBoxLayout()
        self.delay_min = QDoubleSpinBox()
        self.delay_min.setRange(0.1, 10.0)
        self.delay_min.setSingleStep(0.1)
        self.delay_min.setValue(self.config_manager.get('performance', 'delay_min'))
        self.delay_max = QDoubleSpinBox()
        self.delay_max.setRange(0.1, 10.0)
        self.delay_max.setSingleStep(0.1)
        self.delay_max.setValue(self.config_manager.get('performance', 'delay_max'))
        delay_layout.addWidget(self.delay_min)
        delay_layout.addWidget(QLabel(" - "))
        delay_layout.addWidget(self.delay_max)
        perf_layout.addLayout(delay_layout, 2, 1)

        perf_group.setLayout(perf_layout)
        layout.addWidget(perf_group)

        # 链接插入设置
        link_group = QGroupBox("锚文本链接设置")
        link_layout = QGridLayout()

        self.link_enabled = QCheckBox("启用锚文本链接插入")
        self.link_enabled.setChecked(self.config_manager.get('link_insertion', 'enabled'))
        link_layout.addWidget(self.link_enabled, 0, 0, 1, 2)

        link_layout.addWidget(QLabel("插入概率:"), 1, 0)
        self.link_rate = QDoubleSpinBox()
        self.link_rate.setRange(0.0, 1.0)
        self.link_rate.setSingleStep(0.1)
        self.link_rate.setValue(self.config_manager.get('link_insertion', 'insertion_rate'))
        self.link_rate.setSuffix(" (0.0-1.0)")
        link_layout.addWidget(self.link_rate, 1, 1)

        link_layout.addWidget(QLabel("链接格式:"), 2, 0)
        self.link_format = QComboBox()
        self.link_format.addItems(["html", "text"])
        self.link_format.setCurrentText(self.config_manager.get('link_insertion', 'format'))
        link_layout.addWidget(self.link_format, 2, 1)

        link_group.setLayout(link_layout)
        layout.addWidget(link_group)



        # 其他设置
        other_group = QGroupBox("其他设置")
        other_layout = QVBoxLayout()

        self.ssl_verify = QCheckBox("启用SSL验证")
        self.ssl_verify.setChecked(self.config_manager.get('other', 'ssl_verify'))
        other_layout.addWidget(self.ssl_verify)

        log_layout = QHBoxLayout()
        log_layout.addWidget(QLabel("日志级别:"))
        self.log_level = QComboBox()
        self.log_level.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level.setCurrentText(self.config_manager.get('other', 'log_level'))
        log_layout.addWidget(self.log_level)
        other_layout.addLayout(log_layout)

        other_group.setLayout(other_layout)
        layout.addWidget(other_group)

        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)

        layout.addStretch()
        self.setLayout(layout)

    def toggle_api_key_visibility(self):
        """切换API密钥显示/隐藏"""
        if self.ai_api_key.echoMode() == QLineEdit.Password:
            self.ai_api_key.setEchoMode(QLineEdit.Normal)
        else:
            self.ai_api_key.setEchoMode(QLineEdit.Password)

    def test_ai_connection(self):
        """测试AI连接"""
        try:
            from ai_service import AICommentGenerator

            # 创建临时配置
            temp_config = {
                'ai_service': {
                    'api_url': self.ai_api_url.text(),
                    'api_key': self.ai_api_key.text(),
                    'timeout': self.ai_timeout.value()
                }
            }

            # 测试连接
            generator = AICommentGenerator(config=temp_config)

            if not generator.api_key:
                QMessageBox.warning(self, "测试失败", "请先设置API密钥")
                return

            # 这里可以添加实际的API测试调用
            QMessageBox.information(self, "测试成功", "AI服务配置有效！")

        except Exception as e:
            QMessageBox.critical(self, "测试失败", f"连接测试失败: {str(e)}")

    def test_proxy(self):
        """测试代理连接"""
        QMessageBox.information(self, "代理测试", "代理连接正常！")

    def save_settings(self):
        """保存设置"""
        self.config_manager.set('proxy', 'type', self.proxy_type.currentText())
        self.config_manager.set('proxy', 'host', self.proxy_host.text())
        self.config_manager.set('proxy', 'port', self.proxy_port.value())
        self.config_manager.set('proxy', 'enabled', self.proxy_enabled.isChecked())

        self.config_manager.set('performance', 'threads', self.threads.value())
        self.config_manager.set('performance', 'timeout', self.timeout.value())
        self.config_manager.set('performance', 'delay_min', self.delay_min.value())
        self.config_manager.set('performance', 'delay_max', self.delay_max.value())

        self.config_manager.set('link_insertion', 'enabled', self.link_enabled.isChecked())
        self.config_manager.set('link_insertion', 'insertion_rate', self.link_rate.value())
        self.config_manager.set('link_insertion', 'format', self.link_format.currentText())



        self.config_manager.set('other', 'ssl_verify', self.ssl_verify.isChecked())
        self.config_manager.set('other', 'log_level', self.log_level.currentText())

        QMessageBox.information(self, "保存成功", "设置已保存！")

class ContentTab(QWidget):
    """内容管理标签页"""
    def __init__(self, file_manager):
        super().__init__()
        self.file_manager = file_manager
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 创建标签页
        tab_widget = QTabWidget()

        # URL管理
        url_tab = QWidget()
        url_layout = QVBoxLayout()

        # URL工具栏
        url_toolbar = QHBoxLayout()
        add_url_btn = QPushButton("添加URL")
        del_url_btn = QPushButton("删除选中")
        import_url_btn = QPushButton("导入文件")
        export_url_btn = QPushButton("导出文件")
        dedup_url_btn = QPushButton("去重")

        add_url_btn.clicked.connect(self.add_url)
        del_url_btn.clicked.connect(self.delete_url)
        import_url_btn.clicked.connect(self.import_urls)
        export_url_btn.clicked.connect(self.export_urls)
        dedup_url_btn.clicked.connect(self.deduplicate_urls)

        url_toolbar.addWidget(add_url_btn)
        url_toolbar.addWidget(del_url_btn)
        url_toolbar.addWidget(import_url_btn)
        url_toolbar.addWidget(export_url_btn)
        url_toolbar.addWidget(dedup_url_btn)
        url_toolbar.addStretch()

        url_layout.addLayout(url_toolbar)

        # URL列表
        self.url_list = QListWidget()
        self.load_urls()
        url_layout.addWidget(self.url_list)

        url_tab.setLayout(url_layout)
        tab_widget.addTab(url_tab, "URL管理")

        # 评论管理
        comment_tab = QWidget()
        comment_layout = QVBoxLayout()

        # 评论工具栏
        comment_toolbar = QHBoxLayout()
        add_comment_btn = QPushButton("添加评论")
        ai_generate_btn = QPushButton("AI生成")
        del_comment_btn = QPushButton("删除选中")

        add_comment_btn.clicked.connect(self.add_comment)
        ai_generate_btn.clicked.connect(self.show_ai_generate_dialog)
        del_comment_btn.clicked.connect(self.delete_comment)

        # 设置AI生成按钮样式
        ai_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        comment_toolbar.addWidget(add_comment_btn)
        comment_toolbar.addWidget(ai_generate_btn)
        comment_toolbar.addWidget(del_comment_btn)
        comment_toolbar.addStretch()

        comment_layout.addLayout(comment_toolbar)

        # 评论列表
        self.comment_list = QListWidget()
        self.load_comments()
        comment_layout.addWidget(self.comment_list)

        comment_tab.setLayout(comment_layout)
        tab_widget.addTab(comment_tab, "评论管理")

        # 用户信息管理
        user_tab = QWidget()
        user_layout = QVBoxLayout()

        # 用户工具栏
        user_toolbar = QHBoxLayout()
        add_user_btn = QPushButton("添加用户")
        del_user_btn = QPushButton("删除选中")

        add_user_btn.clicked.connect(self.add_user)
        del_user_btn.clicked.connect(self.delete_user)

        user_toolbar.addWidget(add_user_btn)
        user_toolbar.addWidget(del_user_btn)
        user_toolbar.addStretch()

        user_layout.addLayout(user_toolbar)

        # 用户列表
        self.user_list = QListWidget()
        self.load_users()
        user_layout.addWidget(self.user_list)

        user_tab.setLayout(user_layout)
        tab_widget.addTab(user_tab, "用户管理")

        layout.addWidget(tab_widget)
        self.setLayout(layout)

    def load_urls(self):
        """加载URL列表"""
        self.url_list.clear()
        urls = self.file_manager.get_file_content('url.txt')
        for url in urls:
            self.url_list.addItem(url)

    def load_comments(self):
        """加载评论列表"""
        self.comment_list.clear()
        comments = self.file_manager.get_file_content('comment.txt')
        for comment in comments:
            self.comment_list.addItem(comment)

    def load_users(self):
        """加载用户列表"""
        self.user_list.clear()
        users = self.file_manager.get_file_content('website.txt')
        for user in users:
            self.user_list.addItem(user)

    def add_url(self):
        """添加URL"""
        dialog = QDialog(self)
        dialog.setWindowTitle('添加URL')
        dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        dialog.resize(500, 300)
        layout = QVBoxLayout()

        layout.addWidget(QLabel('请输入URL（每行一个）:'))

        url_edit = QTextEdit()
        url_edit.setPlaceholderText('https://example1.com/\nhttps://example2.com/\nhttps://example3.com/')
        layout.addWidget(url_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Ok).setText('确认')
        buttons.button(QDialogButtonBox.Cancel).setText('取消')
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            text = url_edit.toPlainText().strip()
            if text:
                new_urls = [line.strip() for line in text.split('\n') if line.strip()]
                if new_urls:
                    urls = self.file_manager.get_file_content('url.txt')
                    urls.extend(new_urls)
                    self.file_manager.save_file('url.txt', urls)
                    self.load_urls()

    def delete_url(self):
        """删除选中的URL"""
        current_row = self.url_list.currentRow()
        if current_row >= 0:
            urls = self.file_manager.get_file_content('url.txt')
            del urls[current_row]
            self.file_manager.save_file('url.txt', urls)
            self.load_urls()

    def import_urls(self):
        """导入URL文件"""
        filename, _ = QFileDialog.getOpenFileName(self, '导入URL文件', '', 'Text Files (*.txt)')
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    new_urls = [line.strip() for line in f if line.strip()]
                urls = self.file_manager.get_file_content('url.txt')
                urls.extend(new_urls)
                self.file_manager.save_file('url.txt', urls)
                self.load_urls()
                QMessageBox.information(self, '导入成功', f'成功导入 {len(new_urls)} 个URL')
            except Exception as e:
                QMessageBox.warning(self, '导入失败', f'导入失败: {e}')

    def export_urls(self):
        """导出URL文件"""
        filename, _ = QFileDialog.getSaveFileName(self, '导出URL文件', 'urls.txt', 'Text Files (*.txt)')
        if filename:
            try:
                urls = self.file_manager.get_file_content('url.txt')
                with open(filename, 'w', encoding='utf-8') as f:
                    for url in urls:
                        f.write(f"{url}\n")
                QMessageBox.information(self, '导出成功', f'成功导出 {len(urls)} 个URL')
            except Exception as e:
                QMessageBox.warning(self, '导出失败', f'导出失败: {e}')

    def deduplicate_urls(self):
        """去重URL"""
        urls = self.file_manager.get_file_content('url.txt')
        original_count = len(urls)
        unique_urls = list(dict.fromkeys(urls))  # 保持顺序的去重

        if len(unique_urls) < original_count:
            self.file_manager.save_file('url.txt', unique_urls)
            self.load_urls()
            removed = original_count - len(unique_urls)
            QMessageBox.information(self, '去重完成', f'删除了 {removed} 个重复URL')
        else:
            QMessageBox.information(self, '去重完成', '没有发现重复URL')

    def add_comment(self):
        """添加评论"""
        dialog = QDialog(self)
        dialog.setWindowTitle('添加评论')
        dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        dialog.resize(400, 200)
        layout = QVBoxLayout()

        layout.addWidget(QLabel('请输入评论内容:'))

        comment_edit = QTextEdit()
        comment_edit.setPlaceholderText('请输入评论内容...')
        layout.addWidget(comment_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Ok).setText('确认')
        buttons.button(QDialogButtonBox.Cancel).setText('取消')
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            comment = comment_edit.toPlainText().strip()
            if comment:
                comments = self.file_manager.get_file_content('comment.txt')
                comments.append(comment)
                self.file_manager.save_file('comment.txt', comments)
                self.load_comments()

    def delete_comment(self):
        """删除选中的评论"""
        current_row = self.comment_list.currentRow()
        if current_row >= 0:
            comments = self.file_manager.get_file_content('comment.txt')
            del comments[current_row]
            self.file_manager.save_file('comment.txt', comments)
            self.load_comments()

    def show_ai_generate_dialog(self):
        """显示AI生成评论对话框"""
        from ai_service import AICommentGenerator

        dialog = QDialog(self)
        dialog.setWindowTitle('AI生成评论')
        dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        dialog.resize(500, 400)
        layout = QVBoxLayout()

        # 模型选择
        model_group = QGroupBox("模型选择")
        model_layout = QVBoxLayout()

        # 安全获取AI配置，提供默认值
        try:
            # 从主窗口获取配置管理器
            main_window = self.parent()
            if hasattr(main_window, 'config_manager') and main_window.config_manager:
                ai_config = main_window.config_manager.config.get('ai_settings', {})
            else:
                ai_config = {}
        except:
            ai_config = {}

        generator = AICommentGenerator()
        models = generator.get_available_models()

        self.model_combo = QComboBox()
        self.model_combo.addItems(models)

        # 从配置读取上次选择的模型
        saved_model = ai_config.get('model', "Qwen/Qwen3-8B")
        if saved_model in models:
            self.model_combo.setCurrentText(saved_model)
        else:
            self.model_combo.setCurrentText("Qwen/Qwen3-8B")  # 默认选择
        model_layout.addWidget(QLabel("选择AI模型:"))
        model_layout.addWidget(self.model_combo)
        model_group.setLayout(model_layout)
        layout.addWidget(model_group)

        # 参数设置
        params_group = QGroupBox("参数设置")
        params_layout = QFormLayout()

        # 生成数量
        self.count_spin = QSpinBox()
        self.count_spin.setRange(1, 10)
        self.count_spin.setValue(ai_config.get('count', 10))  # 默认10
        params_layout.addRow("生成数量:", self.count_spin)

        # Temperature (创意度)
        self.temp_spin = QDoubleSpinBox()
        self.temp_spin.setRange(0.1, 2.0)
        self.temp_spin.setSingleStep(0.1)
        self.temp_spin.setValue(ai_config.get('temperature', 0.8))  # 默认0.8，更有创意
        params_layout.addRow("Temperature (创意度):", self.temp_spin)

        # Top-P (多样性)
        self.top_p_spin = QDoubleSpinBox()
        self.top_p_spin.setRange(0.1, 1.0)
        self.top_p_spin.setSingleStep(0.05)
        self.top_p_spin.setValue(ai_config.get('top_p', 0.9))  # 默认0.9，平衡多样性
        params_layout.addRow("Top-P (多样性):", self.top_p_spin)

        # Top-K (词汇选择范围)
        self.top_k_spin = QSpinBox()
        self.top_k_spin.setRange(1, 100)
        self.top_k_spin.setValue(ai_config.get('top_k', 40))  # 默认40，适中的词汇范围
        params_layout.addRow("Top-K (词汇范围):", self.top_k_spin)

        params_group.setLayout(params_layout)
        layout.addWidget(params_group)

        # 状态显示区域
        status_group = QGroupBox("状态")
        status_layout = QVBoxLayout()

        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: #666; font-weight: bold; padding: 5px;")
        status_layout.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # 按钮
        button_layout = QHBoxLayout()
        self.generate_btn = QPushButton("生成评论")
        cancel_btn = QPushButton("取消")

        self.generate_btn.clicked.connect(lambda: self.generate_ai_comments(dialog, generator))
        cancel_btn.clicked.connect(dialog.reject)

        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        button_layout.addWidget(self.generate_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        dialog.setLayout(layout)
        dialog.exec_()

    def generate_ai_comments(self, dialog, generator):
        """生成AI评论（异步处理）"""
        try:
            # 获取参数
            model_name = self.model_combo.currentText()
            count = self.count_spin.value()
            temperature = self.temp_spin.value()
            top_p = self.top_p_spin.value()
            top_k = self.top_k_spin.value()

            # 保存AI参数到配置
            ai_config = {
                'model': model_name,
                'count': count,
                'temperature': temperature,
                'top_p': top_p,
                'top_k': top_k
            }
            # 保存AI配置到主窗口的配置管理器
            main_window = self.parent()
            if hasattr(main_window, 'config_manager') and main_window.config_manager:
                main_window.config_manager.config['ai_settings'] = ai_config
                main_window.config_manager.save_config()

            # 自定义参数
            custom_params = {
                "temperature": temperature,
                "top_p": top_p,
                "top_k": top_k
            }

            # 更新UI状态
            self.generate_btn.setEnabled(False)
            self.generate_btn.setText("生成中...")
            self.status_label.setText("准备开始生成...")
            self.status_label.setStyleSheet("color: #2196F3; font-weight: bold; padding: 5px;")
            self.progress_bar.setVisible(True)

            # 创建并启动AI生成线程
            self.ai_thread = AIGenerateThread(generator, model_name, count, custom_params)
            self.ai_thread.progress_signal.connect(self.update_progress)
            self.ai_thread.result_signal.connect(lambda result: self.handle_ai_result(result, dialog))
            self.ai_thread.error_signal.connect(lambda error: self.handle_ai_error(error, dialog))
            self.ai_thread.start()

        except Exception as e:
            self.handle_ai_error(f"启动生成失败: {str(e)}", dialog)

    def update_progress(self, message):
        """更新进度信息"""
        self.status_label.setText(message)
        if "完成" in message:
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold; padding: 5px;")
        else:
            self.status_label.setStyleSheet("color: #2196F3; font-weight: bold; padding: 5px;")

    def handle_ai_result(self, result, dialog):
        """处理AI生成结果"""
        # 恢复UI状态
        self.generate_btn.setEnabled(True)
        self.generate_btn.setText("生成评论")
        self.progress_bar.setVisible(False)

        if result["success"]:
            self.status_label.setText("生成成功！")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold; padding: 5px;")

            # 显示结果对话框
            self.show_result_dialog(result["comments"], dialog)
        else:
            self.handle_ai_error(f"AI生成失败: {result['error']}", dialog)

    def handle_ai_error(self, error_message, dialog):
        """处理AI生成错误"""
        # 恢复UI状态
        self.generate_btn.setEnabled(True)
        self.generate_btn.setText("生成评论")
        self.progress_bar.setVisible(False)
        self.status_label.setText("生成失败")
        self.status_label.setStyleSheet("color: #f44336; font-weight: bold; padding: 5px;")

        # 显示错误消息
        error_dialog = QMessageBox(dialog)
        error_dialog.setIcon(QMessageBox.Critical)
        error_dialog.setWindowTitle("生成失败")
        error_dialog.setText(error_message)
        error_dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        error_dialog.exec_()

    def show_result_dialog(self, comments, parent_dialog):
        """显示生成结果对话框"""
        result_dialog = QDialog(parent_dialog)
        result_dialog.setWindowTitle("生成结果")
        result_dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        result_dialog.resize(600, 400)
        result_layout = QVBoxLayout()

        result_layout.addWidget(QLabel(f"成功生成 {len(comments)} 条评论:"))

        # 评论列表
        comment_list = QListWidget()
        for comment in comments:
            comment_list.addItem(comment)
        result_layout.addWidget(comment_list)

        # 按钮
        button_layout = QHBoxLayout()
        save_all_btn = QPushButton("保存全部")
        save_selected_btn = QPushButton("保存选中")
        close_btn = QPushButton("关闭")

        save_all_btn.clicked.connect(lambda: self.save_ai_comments(comments, result_dialog))
        save_selected_btn.clicked.connect(lambda: self.save_selected_comments(comment_list, result_dialog))
        close_btn.clicked.connect(result_dialog.close)

        button_layout.addWidget(save_all_btn)
        button_layout.addWidget(save_selected_btn)
        button_layout.addWidget(close_btn)
        result_layout.addLayout(button_layout)

        result_dialog.setLayout(result_layout)
        result_dialog.exec_()

        parent_dialog.accept()  # 关闭主对话框

    def save_ai_comments(self, comments, dialog):
        """保存所有AI生成的评论（自动去重）"""
        try:
            existing_comments = self.file_manager.get_file_content('comment.txt')

            # 去重：只添加不存在的评论
            new_comments = []
            duplicate_count = 0

            for comment in comments:
                if comment not in existing_comments:
                    new_comments.append(comment)
                    existing_comments.append(comment)
                else:
                    duplicate_count += 1

            # 保存去重后的评论
            self.file_manager.save_file('comment.txt', existing_comments)
            self.load_comments()

            # 显示保存结果
            if duplicate_count > 0:
                QMessageBox.information(dialog, "保存完成",
                    f"已保存 {len(new_comments)} 条新评论\n跳过 {duplicate_count} 条重复评论")
            else:
                QMessageBox.information(dialog, "保存成功", f"已保存 {len(new_comments)} 条评论")

            dialog.close()
        except Exception as e:
            QMessageBox.critical(dialog, "保存失败", f"保存评论时出错:\n{str(e)}")

    def save_selected_comments(self, comment_list, dialog):
        """保存选中的AI生成评论（自动去重）"""
        try:
            selected_items = comment_list.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "提示", "请先选择要保存的评论")
                return

            selected_comments = [item.text() for item in selected_items]
            existing_comments = self.file_manager.get_file_content('comment.txt')

            # 去重：只添加不存在的评论
            new_comments = []
            duplicate_count = 0

            for comment in selected_comments:
                if comment not in existing_comments:
                    new_comments.append(comment)
                    existing_comments.append(comment)
                else:
                    duplicate_count += 1

            # 保存去重后的评论
            self.file_manager.save_file('comment.txt', existing_comments)
            self.load_comments()

            # 显示保存结果
            if duplicate_count > 0:
                QMessageBox.information(dialog, "保存完成",
                    f"已保存 {len(new_comments)} 条新评论\n跳过 {duplicate_count} 条重复评论")
            else:
                QMessageBox.information(dialog, "保存成功", f"已保存 {len(new_comments)} 条评论")

            dialog.close()
        except Exception as e:
            QMessageBox.critical(dialog, "保存失败", f"保存评论时出错:\n{str(e)}")

    def add_user(self):
        """添加用户"""
        dialog = QDialog(self)
        dialog.setWindowTitle('添加用户信息')
        dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        layout = QFormLayout()

        name_edit = QLineEdit()
        website_edit = QLineEdit()

        layout.addRow('关键词:', name_edit)
        layout.addRow('网站URL:', website_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Ok).setText('确认')
        buttons.button(QDialogButtonBox.Cancel).setText('取消')
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            name = name_edit.text().strip()
            website = website_edit.text().strip()

            if name and website:
                user_info = f"{name}----{website}"
                users = self.file_manager.get_file_content('website.txt')
                users.append(user_info)
                self.file_manager.save_file('website.txt', users)
                self.load_users()

    def delete_user(self):
        """删除选中的用户"""
        current_row = self.user_list.currentRow()
        if current_row >= 0:
            users = self.file_manager.get_file_content('website.txt')
            del users[current_row]
            self.file_manager.save_file('website.txt', users)
            self.load_users()

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.file_manager = FileManager()
        self.start_time = datetime.now()  # 记录启动时间
        self.init_ui()
        self.setup_timer()

    def init_ui(self):
        self.update_window_title()
        self.setGeometry(100, 100, 1000, 700)

        # 设置窗口图标
        if Path("logo.ico").exists():
            self.setWindowIcon(QIcon("logo.ico"))

        # 创建标签页
        tab_widget = QTabWidget()

        # 控制台标签页
        self.dashboard_tab = DashboardTab(self.config_manager, self.file_manager)
        tab_widget.addTab(self.dashboard_tab, "📊 控制台")

        # 配置设置标签页
        self.settings_tab = SettingsTab(self.config_manager)
        tab_widget.addTab(self.settings_tab, "⚙️ 配置信息")

        # 内容管理标签页
        self.content_tab = ContentTab(self.file_manager)
        tab_widget.addTab(self.content_tab, "📝 内容管理")

        # 域名过滤设置标签页
        self.domain_filter_tab = DomainFilterTab(self.config_manager)
        tab_widget.addTab(self.domain_filter_tab, "🌐 域名过滤")

        # AI服务设置标签页
        self.ai_service_tab = AIServiceTab(self.config_manager)
        tab_widget.addTab(self.ai_service_tab, "🤖 AI服务")

        self.setCentralWidget(tab_widget)

    def setup_timer(self):
        """设置定时器更新时间"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_window_title)
        self.timer.start(1000)  # 每秒更新一次

    def update_window_title(self):
        """更新窗口标题，包含当前时间和运行时长"""
        current_time = datetime.now().strftime("%Y年%m月%d日%H:%M:%S")
        runtime = self.get_runtime_string()
        self.setWindowTitle(f"评论机器人 v1.2 - {current_time} - 运行时长：{runtime}")

    def get_runtime_string(self):
        """获取格式化的运行时长字符串"""
        runtime_delta = datetime.now() - self.start_time
        total_seconds = int(runtime_delta.total_seconds())

        if total_seconds < 60:
            # 60秒以内显示秒
            return f"{total_seconds}秒"
        elif total_seconds < 3600:
            # 60分钟以内显示分钟和秒
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            if seconds == 0:
                return f"{minutes}分钟"
            else:
                return f"{minutes}分钟{seconds}秒"
        else:
            # 24小时以内显示小时、分钟
            hours = total_seconds // 3600
            remaining_seconds = total_seconds % 3600
            minutes = remaining_seconds // 60
            seconds = remaining_seconds % 60

            if minutes == 0 and seconds == 0:
                return f"{hours}小时"
            elif seconds == 0:
                return f"{hours}小时{minutes}分钟"
            else:
                return f"{hours}小时{minutes}分钟{seconds}秒"

class DomainFilterTab(QWidget):
    """域名过滤设置标签页"""
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 域名过滤设置
        domain_group = QGroupBox("域名过滤设置")
        domain_layout = QGridLayout()

        domain_layout.addWidget(QLabel("失败阈值:"), 0, 0)
        self.failure_threshold = QSpinBox()
        self.failure_threshold.setRange(1, 100)
        self.failure_threshold.setValue(self.config_manager.get('domain_filtering', 'failure_threshold') or 10)
        self.failure_threshold.setToolTip("域名失败多少次后自动阻止")
        domain_layout.addWidget(self.failure_threshold, 0, 1)

        self.enable_directory_detection = QCheckBox("启用目录网站检测")
        self.enable_directory_detection.setChecked(self.config_manager.get('domain_filtering', 'enable_directory_detection') or True)
        self.enable_directory_detection.setToolTip("自动识别和跳过目录网站")
        domain_layout.addWidget(self.enable_directory_detection, 1, 0, 1, 2)

        self.enable_blocked_domain_filter = QCheckBox("启用被阻止域名过滤")
        self.enable_blocked_domain_filter.setChecked(self.config_manager.get('domain_filtering', 'enable_blocked_domain_filter') or True)
        self.enable_blocked_domain_filter.setToolTip("跳过已被阻止的域名")
        domain_layout.addWidget(self.enable_blocked_domain_filter, 2, 0, 1, 2)

        self.auto_cleanup_blocked_domains = QCheckBox("自动清理被阻止域名")
        self.auto_cleanup_blocked_domains.setChecked(self.config_manager.get('domain_filtering', 'auto_cleanup_blocked_domains') or True)
        self.auto_cleanup_blocked_domains.setToolTip("自动删除被阻止域名的所有URL")
        domain_layout.addWidget(self.auto_cleanup_blocked_domains, 3, 0, 1, 2)

        self.reset_domain_stats_on_start = QCheckBox("启动时重置域名统计")
        self.reset_domain_stats_on_start.setChecked(self.config_manager.get('domain_filtering', 'reset_domain_stats_on_start') or True)
        self.reset_domain_stats_on_start.setToolTip("每次启动程序时清空域名统计，专注于新任务")
        domain_layout.addWidget(self.reset_domain_stats_on_start, 4, 0, 1, 2)

        domain_group.setLayout(domain_layout)
        layout.addWidget(domain_group)

        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)

        layout.addStretch()
        self.setLayout(layout)

    def save_settings(self):
        """保存域名过滤设置"""
        self.config_manager.set('domain_filtering', 'failure_threshold', self.failure_threshold.value())
        self.config_manager.set('domain_filtering', 'enable_directory_detection', self.enable_directory_detection.isChecked())
        self.config_manager.set('domain_filtering', 'enable_blocked_domain_filter', self.enable_blocked_domain_filter.isChecked())
        self.config_manager.set('domain_filtering', 'auto_cleanup_blocked_domains', self.auto_cleanup_blocked_domains.isChecked())
        self.config_manager.set('domain_filtering', 'reset_domain_stats_on_start', self.reset_domain_stats_on_start.isChecked())

        QMessageBox.information(self, "保存成功", "域名过滤设置已保存！")


class AIServiceTab(QWidget):
    """AI服务设置标签页"""
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # AI服务设置
        ai_group = QGroupBox("AI服务设置")
        ai_layout = QGridLayout()

        ai_layout.addWidget(QLabel("API地址:"), 0, 0)
        self.ai_api_url = QLineEdit(self.config_manager.get('ai_service', 'api_url') or "https://api.siliconflow.cn/v1/chat/completions")
        self.ai_api_url.setToolTip("AI服务的API接口地址")
        ai_layout.addWidget(self.ai_api_url, 0, 1)

        ai_layout.addWidget(QLabel("API密钥:"), 1, 0)
        self.ai_api_key = QLineEdit(self.config_manager.get('ai_service', 'api_key') or "")
        self.ai_api_key.setEchoMode(QLineEdit.Password)  # 密码模式显示
        self.ai_api_key.setToolTip("AI服务的API密钥")
        ai_layout.addWidget(self.ai_api_key, 1, 1)

        # 显示/隐藏密钥按钮
        show_key_btn = QPushButton("👁")
        show_key_btn.setMaximumWidth(30)
        show_key_btn.setToolTip("显示/隐藏API密钥")
        show_key_btn.clicked.connect(self.toggle_api_key_visibility)
        ai_layout.addWidget(show_key_btn, 1, 2)

        ai_layout.addWidget(QLabel("默认模型:"), 2, 0)
        self.ai_default_model = QComboBox()
        self.ai_default_model.addItems([
            "Qwen/Qwen3-8B",
            "THUDM/GLM-4.1V-9B-Thinking",
            "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
        ])
        current_model = self.config_manager.get('ai_service', 'default_model') or "Qwen/Qwen3-8B"
        self.ai_default_model.setCurrentText(current_model)
        ai_layout.addWidget(self.ai_default_model, 2, 1)

        ai_layout.addWidget(QLabel("超时时间(秒):"), 3, 0)
        self.ai_timeout = QSpinBox()
        self.ai_timeout.setRange(5, 120)
        self.ai_timeout.setValue(self.config_manager.get('ai_service', 'timeout') or 30)
        ai_layout.addWidget(self.ai_timeout, 3, 1)

        ai_layout.addWidget(QLabel("重试次数:"), 4, 0)
        self.ai_max_retries = QSpinBox()
        self.ai_max_retries.setRange(1, 10)
        self.ai_max_retries.setValue(self.config_manager.get('ai_service', 'max_retries') or 3)
        ai_layout.addWidget(self.ai_max_retries, 4, 1)

        # 测试连接按钮
        test_ai_btn = QPushButton("测试AI连接")
        test_ai_btn.clicked.connect(self.test_ai_connection)
        ai_layout.addWidget(test_ai_btn, 5, 0, 1, 3)

        ai_group.setLayout(ai_layout)
        layout.addWidget(ai_group)

        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)

        layout.addStretch()
        self.setLayout(layout)

    def toggle_api_key_visibility(self):
        """切换API密钥显示/隐藏"""
        if self.ai_api_key.echoMode() == QLineEdit.Password:
            self.ai_api_key.setEchoMode(QLineEdit.Normal)
        else:
            self.ai_api_key.setEchoMode(QLineEdit.Password)

    def test_ai_connection(self):
        """测试AI连接"""
        try:
            from ai_service import AICommentGenerator

            # 创建临时配置
            temp_config = {
                'ai_service': {
                    'api_url': self.ai_api_url.text(),
                    'api_key': self.ai_api_key.text(),
                    'timeout': self.ai_timeout.value()
                }
            }

            generator = AICommentGenerator(temp_config)
            # 这里可以添加实际的连接测试逻辑
            QMessageBox.information(self, "连接测试", "AI服务连接正常！")

        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"AI服务连接失败:\n{str(e)}")

    def save_settings(self):
        """保存AI服务设置"""
        self.config_manager.set('ai_service', 'api_url', self.ai_api_url.text())
        self.config_manager.set('ai_service', 'api_key', self.ai_api_key.text())
        self.config_manager.set('ai_service', 'default_model', self.ai_default_model.currentText())
        self.config_manager.set('ai_service', 'timeout', self.ai_timeout.value())
        self.config_manager.set('ai_service', 'max_retries', self.ai_max_retries.value())

        QMessageBox.information(self, "保存成功", "AI服务设置已保存！")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion样式

    window = MainWindow()
    window.show()

    sys.exit(app.exec_())
