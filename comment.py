#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单评论机器人 - HTTP代理模式
自动发布评论到网站
"""


import re
import time
import random
import json
from datetime import datetime

from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import threading

# HTTP代理模式
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CommentBot:
    def __init__(self, test_proxy=True, config=None):
        # 设置配置参数
        self.config = config or {}

        # 设置代理配置
        self.proxies = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        }

        # 线程锁（用于文件写入）
        self.file_lock = threading.Lock()

        # 从配置获取超时时间
        self.timeout = self.config.get('timeout', 20)

        # 从配置获取延迟范围
        self.delay_min = self.config.get('delay_min', 0.5)
        self.delay_max = self.config.get('delay_max', 1.5)

        # 设置HTTP代理
        if test_proxy:
            self.setup_http_proxy()
        else:
            # 从配置获取代理设置
            proxy_host = self.config.get('proxy_host', '127.0.0.1')
            proxy_port = self.config.get('proxy_port', 7897)
            self.proxies = {
                'http': f'http://{proxy_host}:{proxy_port}',
                'https': f'http://{proxy_host}:{proxy_port}'
            }
        
        # 文件路径
        self.url_file = 'url.txt'
        self.comment_file = 'comment.txt'
        self.website_file = 'website.txt'
        self.success_file = 'success.txt'
        self.error_file = 'error.txt'
        self.progress_file = 'progress.txt'
        self.domain_stats_file = 'domain_stats.json'

        # 域名统计配置
        domain_config = self.config.get('domain_filtering', {})
        self.failure_threshold = domain_config.get('failure_threshold', 10)  # 失败阈值，默认10次
        self.enable_directory_detection = domain_config.get('enable_directory_detection', True)
        self.enable_blocked_domain_filter = domain_config.get('enable_blocked_domain_filter', True)
        self.auto_cleanup_blocked_domains = domain_config.get('auto_cleanup_blocked_domains', True)
        self.reset_domain_stats_on_start = domain_config.get('reset_domain_stats_on_start', True)  # 启动时重置统计
        
        # 确保文件存在
        self._ensure_files_exist()

        # 初始化域名统计
        self._init_domain_stats()

        # 加载CMS规则
        self.cms_rules = self._load_cms_rules()
    
    def setup_http_proxy(self):
        """设置HTTP代理（规则模式）"""
        print("正在设置HTTP代理（规则模式）...")

        # 直接使用7897端口
        port = 7897

        try:
            print(f"测试端口 {port}...")

            # 设置代理配置
            self.proxies = {
                'http': f'http://127.0.0.1:{port}',
                'https': f'http://127.0.0.1:{port}'
            }

            # 测试代理连接
            test_response = requests.get(
                "http://httpbin.org/ip",
                proxies=self.proxies,
                timeout=self.timeout
            )

            if test_response.status_code == 200:
                data = test_response.json()
                ip = data.get('origin', 'Unknown')
                print(f"✅ 端口 {port} 代理测试成功！当前IP: {ip}")
                print(f"✅ 使用端口 {port} 的HTTP代理")
            else:
                print(f"❌ 端口 {port} 代理测试失败")
                exit(1)

        except Exception as e:
            print(f"❌ 端口 {port} 测试失败: {e}")
            exit(1)
    
    def _ensure_files_exist(self):
        """确保必要的文件存在"""
        files = [self.url_file, self.comment_file, self.website_file]
        for file_path in files:
            if not Path(file_path).exists():
                Path(file_path).touch()
                print(f"创建文件: {file_path}")

    def _init_domain_stats(self):
        """初始化域名统计文件"""
        # 如果配置为启动时重置，或文件不存在，则创建新文件
        if self.reset_domain_stats_on_start or not Path(self.domain_stats_file).exists():
            with open(self.domain_stats_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            if self.reset_domain_stats_on_start and Path(self.domain_stats_file).exists():
                print(f"🔄 重置域名统计文件: {self.domain_stats_file}")
            else:
                print(f"📊 创建域名统计文件: {self.domain_stats_file}")

    def _load_domain_stats(self):
        """加载域名统计数据"""
        try:
            if Path(self.domain_stats_file).exists():
                with open(self.domain_stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载域名统计失败: {e}")
            return {}

    def _save_domain_stats(self, stats):
        """保存域名统计数据"""
        try:
            with open(self.domain_stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存域名统计失败: {e}")

    def track_domain_failure(self, url):
        """记录域名失败次数"""
        domain = urlparse(url).netloc
        stats = self._load_domain_stats()

        if domain not in stats:
            stats[domain] = {
                'attempts': 0,
                'failures': 0,
                'last_attempt': None,
                'status': 'active'
            }

        # 更新统计信息
        stats[domain]['attempts'] += 1
        stats[domain]['failures'] += 1
        stats[domain]['last_attempt'] = datetime.now().isoformat()

        # 检查是否超过阈值
        if stats[domain]['failures'] >= self.failure_threshold:
            stats[domain]['status'] = 'blocked'
            print(f"🚫 域名 {domain} 失败次数达到阈值({self.failure_threshold})，已标记为blocked")

        self._save_domain_stats(stats)
        return stats[domain]['status'] == 'blocked'

    def track_domain_success(self, url):
        """记录域名成功"""
        domain = urlparse(url).netloc
        stats = self._load_domain_stats()

        if domain not in stats:
            stats[domain] = {
                'attempts': 0,
                'failures': 0,
                'successes': 0,
                'last_attempt': None,
                'last_success': None,
                'status': 'active'
            }

        # 更新统计信息
        stats[domain]['attempts'] += 1
        stats[domain]['successes'] = stats[domain].get('successes', 0) + 1
        stats[domain]['last_attempt'] = datetime.now().isoformat()
        stats[domain]['last_success'] = datetime.now().isoformat()

        self._save_domain_stats(stats)

    def is_domain_blocked(self, url):
        """检查域名是否被阻止"""
        domain = urlparse(url).netloc
        stats = self._load_domain_stats()
        return stats.get(domain, {}).get('status') == 'blocked'

    def is_directory_site(self, url):
        """检测是否为目录网站"""
        parsed_url = urlparse(url)
        path = parsed_url.path.strip('/')

        # 检查路径层级数量（目录网站通常有很多层级）
        path_segments = [seg for seg in path.split('/') if seg]
        if len(path_segments) > 6:  # 超过6层路径很可能是目录网站
            return True

        # 检查是否包含重复的路径段（目录网站的特征）
        if len(path_segments) != len(set(path_segments)):
            return True

        # 检查常见的目录网站域名模式
        directory_patterns = [
            r'directory',
            r'dir\.',
            r'list',
            r'catalog',
            r'index',
            r'links'
        ]

        domain = parsed_url.netloc.lower()
        for pattern in directory_patterns:
            if re.search(pattern, domain):
                return True

        # 检查URL路径中的目录网站特征
        path_lower = path.lower()
        directory_path_patterns = [
            r'/arts/.*?/arts/',  # 重复的arts路径
            r'/business/.*?/business/',  # 重复的business路径
            r'/computers/.*?/computers/',  # 重复的computers路径
            r'/health/.*?/health/',  # 重复的health路径
            r'/home/<USER>/home/',  # 重复的home路径
            r'/recreation/.*?/recreation/',  # 重复的recreation路径
        ]

        for pattern in directory_path_patterns:
            if re.search(pattern, path_lower):
                return True

        return False

    def remove_domain_urls(self, domain):
        """批量删除指定域名的所有URL"""
        try:
            if not Path(self.url_file).exists():
                return 0

            with open(self.url_file, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]

            # 找出需要删除的URL
            urls_to_remove = []
            remaining_urls = []

            for url in urls:
                url_domain = urlparse(url).netloc
                if url_domain == domain:
                    urls_to_remove.append(url)
                else:
                    remaining_urls.append(url)

            # 如果有URL需要删除，重写文件
            if urls_to_remove:
                with open(self.url_file, 'w', encoding='utf-8') as f:
                    for url in remaining_urls:
                        f.write(f"{url}\n")

                print(f"🗑️ 已删除域名 {domain} 的 {len(urls_to_remove)} 个URL")
                return len(urls_to_remove)

            return 0
        except Exception as e:
            print(f"批量删除URL失败: {e}")
            return 0

    def get_domain_stats_report(self):
        """生成域名统计报告"""
        stats = self._load_domain_stats()
        if not stats:
            return "暂无域名统计数据"

        report = ["域名统计报告", "=" * 50]

        # 按失败次数排序
        sorted_domains = sorted(stats.items(), key=lambda x: x[1].get('failures', 0), reverse=True)

        for domain, data in sorted_domains:
            attempts = data.get('attempts', 0)
            failures = data.get('failures', 0)
            successes = data.get('successes', 0)
            status = data.get('status', 'active')
            last_attempt = data.get('last_attempt', 'N/A')

            success_rate = (successes / attempts * 100) if attempts > 0 else 0

            report.append(f"\n域名: {domain}")
            report.append(f"  状态: {status}")
            report.append(f"  尝试次数: {attempts}")
            report.append(f"  失败次数: {failures}")
            report.append(f"  成功次数: {successes}")
            report.append(f"  成功率: {success_rate:.1f}%")
            report.append(f"  最后尝试: {last_attempt}")

        return "\n".join(report)

    def _load_cms_rules(self):
        """加载CMS识别规则"""
        try:
            cms_rules_file = Path('config/cms_rules.json')
            if cms_rules_file.exists():
                with open(cms_rules_file, 'r', encoding='utf-8') as f:
                    rules_data = json.load(f)
                    return rules_data.get('cms_detection_rules', {})
            else:
                print("⚠️ CMS规则文件不存在，使用默认规则")
                return {}
        except Exception as e:
            print(f"加载CMS规则失败: {e}")
            return {}

    def detect_cms_type(self, url, soup):
        """检测CMS类型"""
        detected_cms = []

        for cms_key, cms_config in self.cms_rules.items():
            confidence = 0
            detection_methods = cms_config.get('detection_methods', {})

            # URL模式检测
            url_patterns = detection_methods.get('url_patterns', [])
            for pattern in url_patterns:
                if pattern in url:
                    confidence += 0.3
                    break

            # DOM选择器检测
            dom_selectors = detection_methods.get('dom_selectors', [])
            for selector in dom_selectors:
                try:
                    if soup.select(selector):
                        confidence += 0.2
                        break
                except:
                    continue

            # Meta生成器检测
            meta_generators = detection_methods.get('meta_generators', [])
            meta_generator = soup.find('meta', attrs={'name': 'generator'})
            if meta_generator:
                generator_content = meta_generator.get('content', '').lower()
                for generator in meta_generators:
                    if generator.lower() in generator_content:
                        confidence += 0.4
                        break

            # 如果置信度超过阈值，添加到检测结果
            threshold = cms_config.get('confidence_threshold', 0.5)
            if confidence >= threshold:
                detected_cms.append((cms_key, confidence, cms_config))

        # 按置信度排序，返回最匹配的CMS
        if detected_cms:
            detected_cms.sort(key=lambda x: x[1], reverse=True)
            return detected_cms[0]  # 返回 (cms_key, confidence, cms_config)

        return None

    def reset_domain_stats(self):
        """手动重置域名统计"""
        try:
            with open(self.domain_stats_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            print("🔄 域名统计已重置")
            return True
        except Exception as e:
            print(f"重置域名统计失败: {e}")
            return False

    def get_domain_stats_summary(self):
        """获取域名统计摘要"""
        stats = self._load_domain_stats()
        if not stats:
            return "暂无域名统计数据"

        total_domains = len(stats)
        blocked_domains = len([d for d in stats.values() if d.get('status') == 'blocked'])
        active_domains = total_domains - blocked_domains

        return f"域名统计摘要: 总计 {total_domains} 个域名，活跃 {active_domains} 个，已阻止 {blocked_domains} 个"
    
    def get_current_url(self):
        """获取当前要处理的URL"""
        if not Path(self.url_file).exists():
            print(f"错误: {self.url_file} 文件不存在")
            return None

        # 读取所有URL
        with open(self.url_file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]

        if not urls:
            print("URL文件为空")
            return None

        # 读取已处理的域名（成功和失败的）
        processed_domains = set()

        # 从success.txt读取成功的域名
        if Path(self.success_file).exists():
            with open(self.success_file, 'r', encoding='utf-8') as f:
                for line in f:
                    article_url = line.strip()
                    if article_url:
                        from urllib.parse import urlparse
                        domain = urlparse(article_url).netloc
                        processed_domains.add(domain)

        # 从error.txt读取失败的域名
        if Path(self.error_file).exists():
            with open(self.error_file, 'r', encoding='utf-8') as f:
                for line in f:
                    error_url = line.strip()
                    if error_url:
                        from urllib.parse import urlparse
                        domain = urlparse(error_url).netloc
                        processed_domains.add(domain)

        # 找到第一个未处理的URL（基于域名）
        for url in urls:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc
            if domain not in processed_domains:
                return url

        # 所有URL都已处理
        print("所有URL已处理完成")
        return None
    
    def find_latest_article(self, base_url):
        """查找最新文章"""
        # 确保使用HTTPS，直接访问不要跳转
        if not base_url.startswith('https://') and not base_url.startswith('http://'):
            base_url = 'https://' + base_url
        elif base_url.startswith('http://'):
            base_url = base_url.replace('http://', 'https://')

        try:
            print(f"正在通过HTTP代理分析网站: {base_url}")
            # 直接使用requests.get
            response = requests.get(
                base_url,
                proxies=self.proxies,
                headers=self.headers,
                timeout=self.timeout,
                verify=False
            )

            print(f"网站响应状态码: {response.status_code}")

            # 检查状态码
            if response.status_code != 200:
                error_msg = f"HTTP {response.status_code}"
                if response.status_code == 403:
                    error_msg += " (访问被拒绝)"
                elif response.status_code == 404:
                    error_msg += " (页面不存在)"
                elif response.status_code == 500:
                    error_msg += " (服务器内部错误)"
                elif response.status_code == 502:
                    error_msg += " (网关错误)"
                elif response.status_code == 503:
                    error_msg += " (服务不可用)"

                self.save_error(base_url)
                print(f"❌ 网站访问失败: {error_msg}")
                return None

            print(f"✅ 成功连接到网站，状态码: {response.status_code}")

        except requests.exceptions.Timeout:
            self.save_error(base_url)
            print(f"❌ 连接超时")
            return None
        except requests.exceptions.ConnectionError as e:
            self.save_error(base_url)
            print(f"❌ 连接错误: {e}")
            return None
        except Exception as e:
            self.save_error(base_url)
            print(f"❌ 连接失败: {e}")
            return None

        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # 常见的文章链接模式
            article_selectors = [
                'article a[href]',
                '.post a[href]',
                '.entry a[href]',
                'h2 a[href]',
                'h3 a[href]',
                '.post-title a[href]',
                '.entry-title a[href]'
            ]

            for selector in article_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href:
                        article_url = urljoin(base_url, href)
                        if self._is_article_url(article_url, base_url):
                            print(f"找到文章: {article_url}")
                            return article_url

            # 如果没找到，从所有链接中找
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link.get('href')
                article_url = urljoin(base_url, href)
                if self._is_article_url(article_url, base_url):
                    print(f"从首页链接找到文章: {article_url}")
                    return article_url

            print("未找到合适的文章链接")
            return None

        except Exception as e:
            print(f"解析网页失败: {e}")
            return None
    
    def _is_article_url(self, url, base_url):
        """判断是否是文章URL"""
        parsed_base = urlparse(base_url)
        parsed_url = urlparse(url)
        
        if parsed_url.netloc != parsed_base.netloc:
            return False
        
        # 排除明显不是文章的URL
        exclude_patterns = [
            r'/wp-admin/', r'/wp-content/', r'/wp-includes/',
            r'/feed/', r'/category/', r'/tag/', r'/author/',
            r'/page/', r'/search/', r'\.css$', r'\.js$',
            r'\.jpg$', r'\.png$', r'\.gif$', r'\.pdf$'
        ]
        
        for pattern in exclude_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        # 文章URL特征
        article_patterns = [
            r'/\d{4}/',  # 年份
            r'/\d{2}/',  # 月份
            r'[a-zA-Z0-9-]{10,}',  # 较长的slug
        ]
        
        for pattern in article_patterns:
            if re.search(pattern, url):
                return True
        
        return False
    
    def find_comment_form(self, article_url):
        """查找评论表单（基于CMS规则配置）"""
        try:
            print(f"正在分析文章页面: {article_url}")
            response = requests.get(article_url, proxies=self.proxies, headers=self.headers, timeout=self.timeout)

            # 检查文章页面状态码
            if response.status_code != 200:
                self.save_error(article_url)
                print(f"❌ 文章页面访问失败，状态码: {response.status_code}")
                return None, None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 1. 首先尝试检测CMS类型
            detected_cms = self.detect_cms_type(article_url, soup)
            comment_form = None
            form_type = ""

            if detected_cms:
                cms_key, confidence, cms_config = detected_cms
                print(f"🔍 检测到CMS: {cms_config.get('name', cms_key)} (置信度: {confidence:.2f})")

                # 使用检测到的CMS的表单选择器
                comment_form_config = cms_config.get('comment_form', {})
                selectors = comment_form_config.get('selectors', [])

                for selector in selectors:
                    try:
                        comment_form = soup.select_one(selector)
                        if comment_form:
                            form_type = f"{cms_config.get('name', cms_key)} - {selector}"
                            break
                    except Exception as e:
                        continue

            # 2. 如果没有检测到特定CMS，使用通用策略
            if not comment_form:
                print("🔍 未检测到特定CMS，使用通用表单识别策略")

                # 通用表单选择器（按优先级排序）
                generic_selectors = [
                    # 标准评论表单
                    'form#commentform',
                    'form.comment-form',
                    'form#comment-form',
                    'form[action*="wp-comments-post.php"]',
                    'form[action*="/comment"]',
                    'form[action*="comment"]',
                    'form[action*="post-comment"]',
                    'form[action*="add-comment"]',
                    'form[action*="submit-comment"]',
                    'form[action*="feedback"]',
                    'form[action*="review"]',
                    # ID和Class包含关键词
                    'form[id*="comment"]',
                    'form[class*="comment"]',
                    'form[id*="feedback"]',
                    'form[class*="feedback"]',
                    # Wix特殊选择器
                    'form[data-hook="blog-comment-form"]',
                    'div[data-hook="comments-root"] form',
                    '.wix-form',
                    'form[data-hook*="comment"]',
                ]

                for selector in generic_selectors:
                    try:
                        comment_form = soup.select_one(selector)
                        if comment_form:
                            form_type = f"通用识别 - {selector}"
                            break
                    except:
                        continue

            # 3. 按字段组合识别
            if not comment_form:
                field_selectors = [
                    'form:has(textarea[name*="comment"])',
                    'form:has(textarea[name*="message"])',
                    'form:has(textarea[name*="body"])',
                    'form:has(input[name*="comment"])',
                    'form:has(input[name="name"]):has(input[name="email"]):has(textarea)',
                    'form:has(textarea[data-hook*="comment"])',  # Wix特殊字段
                ]

                for selector in field_selectors:
                    try:
                        comment_form = soup.select_one(selector)
                        if comment_form:
                            form_type = f"字段识别 - {selector}"
                            break
                    except:
                        continue

            # 4. 最后尝试：智能表单识别
            if not comment_form:
                forms = soup.find_all('form')
                for form in forms:
                    # 检查是否包含评论相关字段
                    has_textarea = form.find('textarea')
                    has_name = form.find('input', {'name': re.compile(r'name|author', re.I)})
                    has_email = form.find('input', {'name': re.compile(r'email|mail', re.I)})

                    # Wix特殊字段检查
                    has_wix_comment = form.find('textarea', {'data-hook': re.compile(r'comment', re.I)})
                    has_wix_name = form.find('input', {'data-hook': re.compile(r'name', re.I)})
                    has_wix_email = form.find('input', {'data-hook': re.compile(r'email', re.I)})

                    if (has_textarea and (has_name or has_email)) or (has_wix_comment and (has_wix_name or has_wix_email)):
                        comment_form = form
                        form_type = "智能识别"
                        break

            if comment_form:
                print(f"✅ 找到评论表单 ({form_type})")
                return comment_form, article_url
            else:
                print("❌ 未找到评论表单")
                return None, None

        except Exception as e:
            print(f"分析文章页面失败: {e}")
            return None, None
    
    def get_random_comment(self):
        """获取随机评论"""
        if not Path(self.comment_file).exists():
            return None
        
        with open(self.comment_file, 'r', encoding='utf-8') as f:
            comments = [line.strip() for line in f if line.strip()]
        
        return random.choice(comments) if comments else None
    
    def get_random_website_info(self):
        """获取随机网站信息"""
        if not Path(self.website_file).exists():
            return None, None
        
        with open(self.website_file, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip()]
        
        if not lines:
            return None, None
        
        line = random.choice(lines)
        if '----' in line:
            name, website = line.split('----', 1)
            domain = urlparse(website).netloc
            if domain.startswith('www.'):
                domain = domain[4:]

            # 生成英文邮箱（不带数字）
            email = self._generate_english_email(name.strip(), domain)

            return {
                'name': name.strip(),
                'website': website.strip(),
                'email': email
            }, line
        
        return None, None

    def _generate_english_email(self, name, domain):
        """生成英文邮箱（不带数字）"""
        import re

        # 清理名称，只保留英文字母
        clean_name = re.sub(r'[^a-zA-Z]', '', name.lower())

        # 如果名称为空，使用默认前缀
        if not clean_name:
            clean_name = 'user'

        # 常见的邮箱后缀
        suffixes = [
            '', 'mail', 'contact', 'info', 'hello', 'admin',
            'support', 'team', 'office', 'service', 'help'
        ]

        # 随机选择后缀
        suffix = random.choice(suffixes)

        if suffix:
            email_prefix = f"{clean_name}{suffix}"
        else:
            email_prefix = clean_name

        return f"{email_prefix}@{domain}"

    def enhance_comment_with_anchor_link(self, comment_text, user_info):
        """在评论中添加锚文本链接"""

        # 检查是否启用链接插入
        if not self.config.get('link_insertion', {}).get('enabled', True):
            return comment_text

        # 检查插入概率
        insertion_rate = self.config.get('link_insertion', {}).get('insertion_rate', 0.7)
        if random.random() > insertion_rate:
            return comment_text

        try:
            website_url = user_info['website']
            # 优先使用user_info中的name作为锚文本
            anchor_text = user_info['name']
            link_format = self.config.get('link_insertion', {}).get('format', 'html')

            # 构建链接
            if link_format == 'html':
                link = f'<a href="{website_url}">{anchor_text}</a>'
            else:
                link = f'{anchor_text} ({website_url})'

            # 自然插入模板
            templates = [
                # 推荐型
                f"{comment_text} Check out {link} for more insights.",
                f"{comment_text} You might find {link} helpful too.",
                f"{comment_text} I recommend checking {link} for additional information.",
                f"{comment_text} For anyone interested, {link} has great resources on this.",
                f"{comment_text} If you want to learn more, {link} is worth visiting.",
                
                # 补充型
                f"{comment_text} Similar content at {link}.",
                f"{comment_text} More details: {link}",
                f"{comment_text} Related: {link}",
                f"{comment_text} Also see {link}.",
                f"{comment_text} Visit {link} for more info.",
                f"{comment_text} This reminds me of what I read on {link}.",
                
                # 个人经验型
                f"{comment_text} I found {link} very useful for this topic.",
                f"{comment_text} I learned a lot from {link} about this.",
                f"{comment_text} Based on my experience, {link} offers great insights.",
                f"{comment_text} I've been following {link} for similar content.",
                
                # 问答型
                f"{comment_text} Have you seen {link}? It's relevant to this discussion.",
                f"{comment_text} Wondering if you've checked {link} for more on this?",
                
                # 自然过渡型
                f"{comment_text} By the way, {link} covers this in depth.",
                f"{comment_text} Speaking of which, {link} has some interesting perspectives.",
                f"{comment_text} On a related note, {link} explores this further.",
                
                # 中性陈述型
                f"{comment_text} There's more information available at {link}.",
                f"{comment_text} Additional resources can be found at {link}.",
                f"{comment_text} {link} provides further context on this subject."
            ]

            # 随机选择模板
            enhanced_comment = random.choice(templates)
            print(f"💡 已添加锚文本链接: {anchor_text} -> {website_url}")

            return enhanced_comment

        except Exception as e:
            print(f"⚠️ 添加锚文本链接失败: {e}")
            return comment_text

    def extract_anchor_text(self, url):
        """从URL提取锚文本"""
        try:
            # 首先尝试从website.txt中查找对应的关键词
            if Path(self.website_file).exists():
                with open(self.website_file, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f if line.strip()]
                
                # 查找匹配的URL
                for line in lines:
                    if '----' in line:
                        keyword, website_url = line.split('----', 1)
                        if website_url.strip() == url:
                            print(f"✅ 使用website.txt中的关键词作为锚文本: {keyword.strip()}")
                            return keyword.strip()
            
            # 如果在website.txt中找不到匹配项，则使用域名提取（备用方案）
            from urllib.parse import urlparse
            domain = urlparse(url).netloc

            # 移除www前缀
            if domain.startswith('www.'):
                domain = domain[4:]

            # 提取主域名作为锚文本
            brand_name = domain.split('.')[0]

            # 首字母大写，处理特殊情况
            if len(brand_name) > 1:
                return brand_name.capitalize()
            else:
                return brand_name.upper()

        except Exception as e:
            print(f"⚠️ 提取锚文本失败: {e}")
            return "this site"

    def _fill_comment_fields(self, form, form_data, comment_text, user_info):
        """智能填充表单字段 - 支持多种CMS"""

        # 评论内容字段 (按优先级排序)
        comment_fields = [
            # WordPress标准
            'comment',
            # Drupal
            'comment_body',
            # Blogger
            'commentBody',
            # 国内CMS
            'msg',           # 织梦CMS
            'saytext',       # 帝国CMS
            'content',       # PHPCMS
            'message',       # Discuz论坛
            'inpComment',    # Z-Blog
            # 通用字段
            'body', 'text', 'description', 'review', 'feedback',
            # 其他可能的字段
            'comment_content', 'user_comment', 'post_content'
        ]

        # 用户名字段
        name_fields = [
            # WordPress标准
            'author',
            # 国内CMS
            'username',      # 织梦CMS/帝国CMS
            'inpName',       # Z-Blog
            # Drupal/Joomla
            'name',
            # 通用字段
            'user_name', 'full_name', 'display_name', 'nickname',
            'realname', 'user', 'poster_name'
        ]

        # 邮箱字段
        email_fields = [
            # 标准字段
            'email',
            # 国内CMS变体
            'mail', 'e_mail', 'user_email', 'email_address',
            'inpEmail',      # Z-Blog
            'user_mail', 'poster_email'
        ]

        # 网站字段
        url_fields = [
            # WordPress标准
            'url',
            # 其他变体
            'website', 'homepage', 'site', 'web', 'link',
            'inpHomePage',   # Z-Blog
            'user_url', 'user_website', 'poster_url'
        ]

        # 评分字段
        rating_fields = [
            'rating', 'rate', 'score', 'stars', 'grade',
            'review_rating', 'product_rating', 'star_rating'
        ]

        # 标题字段
        title_fields = [
            'title', 'subject', 'headline', 'summary',
            'review_title', 'comment_title'
        ]

        # 产品相关字段
        product_fields = [
            'product_id', 'item_id', 'review_item',
            'product_review', 'review_product'
        ]

        # 验证码字段（国内CMS常用）
        validate_fields = [
            'validate', 'vdcode', 'yzm', 'code', 'verify',
            'checkcode', 'safecode', 'captcha', 'imgcode'
        ]

        # 文档/文章ID字段（国内CMS常用）
        document_id_fields = [
            'aid', 'id', 'classid', 'catid', 'contentid',
            'docid', 'itemid', 'postid'
        ]

        # 填充评论内容
        for field_name in comment_fields:
            field = form.find('textarea', {'name': field_name}) or form.find('input', {'name': field_name})
            if field:
                form_data[field_name] = comment_text
                print(f"填充评论字段: {field_name}")
                break

        # 填充用户名
        for field_name in name_fields:
            field = form.find('input', {'name': field_name})
            if field:
                form_data[field_name] = user_info['name']
                print(f"填充姓名字段: {field_name}")
                break

        # 填充邮箱
        for field_name in email_fields:
            field = form.find('input', {'name': field_name})
            if field:
                form_data[field_name] = user_info['email']
                print(f"填充邮箱字段: {field_name}")
                break

        # 填充网站
        for field_name in url_fields:
            field = form.find('input', {'name': field_name})
            if field:
                form_data[field_name] = user_info['website']
                print(f"填充网站字段: {field_name}")
                break

        # 填充评分字段（如果存在）
        for field_name in rating_fields:
            field = form.find('input', {'name': field_name}) or form.find('select', {'name': field_name})
            if field:
                # 默认给4-5星的好评
                if field.name == 'select':
                    # 对于下拉选择，选择较高的评分
                    options = field.find_all('option')
                    if options:
                        # 选择倒数第二个选项（通常是4星或很好）
                        selected_option = options[-2] if len(options) > 1 else options[-1]
                        form_data[field_name] = selected_option.get('value', '4')
                else:
                    # 对于输入框，默认给4分（满分5分）
                    form_data[field_name] = '4'
                print(f"Filled rating field: {field_name} = {form_data[field_name]}")
                break

        # Fill title fields (if exists)
        for field_name in title_fields:
            field = form.find('input', {'name': field_name})
            if field:
                # Generate a short title
                title = f"Review by {user_info.get('name', 'User')}"
                form_data[field_name] = title
                print(f"Filled title field: {field_name}")
                break

        # Fill product related fields (if exists)
        for field_name in product_fields:
            field = form.find('input', {'name': field_name})
            if field:
                # Try to get existing value, leave empty if none
                existing_value = field.get('value', '')
                form_data[field_name] = existing_value
                if existing_value:
                    print(f"Filled product field: {field_name} = {existing_value}")
                break

        # 处理特殊字段 (CSRF令牌等)
        self._handle_special_fields(form, form_data)

    def _handle_special_fields(self, form, form_data):
        """处理特殊字段和令牌"""

        # WordPress nonce字段
        nonce_fields = ['_wp_http_referer', 'comment_post_ID', 'comment_parent']
        for field_name in nonce_fields:
            field = form.find('input', {'name': field_name})
            if field:
                value = field.get('value', '')
                form_data[field_name] = value
                print(f"添加WordPress字段: {field_name}")

        # Drupal form_token
        token_field = form.find('input', {'name': 'form_token'})
        if token_field:
            form_data['form_token'] = token_field.get('value', '')
            print("添加Drupal令牌")

        # CSRF令牌 (通用)
        csrf_fields = ['csrf_token', '_token', 'authenticity_token', 'csrfmiddlewaretoken']
        for field_name in csrf_fields:
            field = form.find('input', {'name': field_name})
            if field:
                form_data[field_name] = field.get('value', '')
                print(f"添加CSRF令牌: {field_name}")

        # 国内CMS特殊字段
        special_fields = {
            'validate': '',      # 验证码字段 (暂时留空)
            'id': '',           # 文章ID
            'fid': '',          # 论坛ID (Discuz)
            'tid': '',          # 主题ID (Discuz)
            'pid': '',          # 帖子ID (Discuz)
        }

        for field_name, default_value in special_fields.items():
            field = form.find('input', {'name': field_name})
            if field:
                value = field.get('value', default_value)
                form_data[field_name] = value
                print(f"添加特殊字段: {field_name}")

    def submit_comment(self, form, article_url, comment_text, user_info):
        """提交评论"""
        try:
            action = form.get('action')
            if not action:
                print("表单没有action属性")
                return False
            
            submit_url = urljoin(article_url, action)
            
            # 准备表单数据
            form_data = {}
            
            # 获取隐藏字段
            hidden_inputs = form.find_all('input', type='hidden')
            for hidden in hidden_inputs:
                name = hidden.get('name')
                value = hidden.get('value', '')
                if name:
                    form_data[name] = value

            # 增强评论内容（添加锚文本链接）
            enhanced_comment = self.enhance_comment_with_anchor_link(comment_text, user_info)

            # 智能填充评论数据 - 支持多种字段名
            self._fill_comment_fields(form, form_data, enhanced_comment, user_info)
            
            # 提交按钮
            submit_btn = form.find('input', type='submit')
            if submit_btn and submit_btn.get('name'):
                form_data[submit_btn.get('name')] = submit_btn.get('value', 'Submit')
            
            print(f"提交评论到: {submit_url}")
            print(f"原始评论: {comment_text[:50]}...")
            print(f"增强评论: {enhanced_comment[:80]}...")
            print(f"用户: {user_info['name']} ({user_info['email']})")
            
            # 延迟（从配置读取）
            time.sleep(random.uniform(self.delay_min, self.delay_max))
            
            # 提交
            response = requests.post(
                submit_url,
                data=form_data,
                proxies=self.proxies,
                headers=self.headers,
                timeout=self.timeout,
                allow_redirects=True
            )
            
            if response.status_code == 200 or response.status_code == 302:
                success_keywords = ['success', 'thank', 'submitted', 'pending', 'moderation', 'approved']
                if any(keyword in response.text.lower() for keyword in success_keywords):
                    print("✅ 评论提交成功")
                    return True
                else:
                    print("⚠️ 评论可能提交成功")
                    return True
            else:
                error_msg = f"HTTP {response.status_code}"
                if response.status_code == 403:
                    error_msg += " (访问被拒绝)"
                elif response.status_code == 404:
                    error_msg += " (页面不存在)"
                elif response.status_code == 500:
                    error_msg += " (服务器内部错误)"

                self.save_error(article_url)
                print(f"❌ 评论提交失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"提交评论失败: {e}")
            return False
    
    def save_success(self, article_url):
        """保存成功记录（线程安全）"""
        with self.file_lock:
            # 记录域名成功统计
            self.track_domain_success(article_url)

            with open(self.success_file, 'a', encoding='utf-8') as f:
                f.write(f"{article_url}\n")
        print(f"成功记录已保存: {article_url}")

    def save_error(self, url):
        """保存错误记录 - 集成域名统计和阈值检查（线程安全）"""
        domain = urlparse(url).netloc

        with self.file_lock:
            # 记录域名失败统计
            is_blocked = self.track_domain_failure(url)

            # 如果域名被阻止，批量删除该域名的所有URL
            if is_blocked:
                removed_count = self.remove_domain_urls(domain)
                print(f"🚫 域名 {domain} 已被阻止，删除了 {removed_count} 个相关URL")
                return

            # 检查是否已存在该域名的错误记录
            existing_domains = set()
            if Path(self.error_file).exists():
                with open(self.error_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        error_url = line.strip()
                        if error_url:
                            existing_domain = urlparse(error_url).netloc
                            existing_domains.add(existing_domain)

            # 如果域名不存在，才添加到错误文件
            if domain not in existing_domains:
                with open(self.error_file, 'a', encoding='utf-8') as f:
                    f.write(f"{url}\n")
                print(f"错误记录已保存: {url}")
            else:
                print(f"域名已存在错误记录，跳过: {domain}")

            # 从url.txt中删除该URL（在同一个锁内执行）
            self._remove_url_from_file_unsafe(url)

    def _remove_url_from_file_unsafe(self, url_to_remove):
        """从url.txt中删除指定URL（不安全版本，需要在锁内调用）"""
        try:
            if Path(self.url_file).exists():
                with open(self.url_file, 'r', encoding='utf-8') as f:
                    urls = [line.strip() for line in f if line.strip()]

                # 删除指定URL
                updated_urls = [url for url in urls if url != url_to_remove]

                if len(updated_urls) < len(urls):
                    with open(self.url_file, 'w', encoding='utf-8') as f:
                        for url in updated_urls:
                            f.write(f"{url}\n")
                    print(f"已从URL列表中删除: {url_to_remove}")
                else:
                    print(f"URL不存在于列表中: {url_to_remove}")
        except Exception as e:
            print(f"删除URL失败: {e}")

    def remove_url_from_file(self, url_to_remove):
        """从url.txt中删除指定URL（线程安全）"""
        with self.file_lock:  # 使用线程锁保护文件操作
            self._remove_url_from_file_unsafe(url_to_remove)
    
    def process_single_url(self, current_url):
        """处理单个URL - 集成预检查和智能过滤"""
        thread_id = threading.current_thread().name
        print(f"[{thread_id}] 处理URL: {current_url}")

        try:
            # 预检查1: 检查域名是否被阻止
            if self.is_domain_blocked(current_url):
                domain = urlparse(current_url).netloc
                print(f"[{thread_id}] 🚫 域名 {domain} 已被阻止，跳过")
                return

            # 预检查2: 检查是否为目录网站（如果启用）
            if self.enable_directory_detection and self.is_directory_site(current_url):
                print(f"[{thread_id}] 📁 检测到目录网站，跳过: {current_url}")
                self.save_error(current_url)
                return

            print(f"[{thread_id}] ✅ 预检查通过，开始处理")

            # 查找文章
            article_url = self.find_latest_article(current_url)
            if not article_url:
                print(f"[{thread_id}] ❌ 未找到文章")
                self.save_error(current_url)
                return

            # 查找评论表单
            form, article_url = self.find_comment_form(article_url)
            if not form:
                print(f"[{thread_id}] ❌ 未找到评论表单")
                self.save_error(current_url)
                return

            # 获取评论内容
            comment_text = self.get_random_comment()
            if not comment_text:
                print(f"[{thread_id}] ❌ 未获取到评论内容，跳过此网站")
                return

            # 获取用户信息
            user_info, _ = self.get_random_website_info()
            if not user_info:
                print(f"[{thread_id}] ❌ 未获取到用户信息，跳过此网站")
                return

            # 提交评论
            success = self.submit_comment(form, article_url, comment_text, user_info)

            if success:
                self.save_success(article_url)
                print(f"[{thread_id}] 🎉 任务完成！")
            else:
                print(f"[{thread_id}] ❌ 评论提交失败")
                self.save_error(current_url)

        except Exception as e:
            print(f"[{thread_id}] ❌ 处理网站时出错: {e}")
            self.save_error(current_url)

        print(f"[{thread_id}] " + "-" * 50)

    def deduplicate_urls(self):
        """去除url.txt中的重复URL（线程安全）"""
        with self.file_lock:  # 使用线程锁保护文件操作
            if not Path(self.url_file).exists():
                print(f"错误: {self.url_file} 文件不存在")
                return

            # 读取所有URL
            with open(self.url_file, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]

            if not urls:
                print("URL文件为空")
                return

            # 去重（保持顺序）
            unique_urls = []
            seen = set()
            duplicates = []

            for url in urls:
                if url not in seen:
                    unique_urls.append(url)
                    seen.add(url)
                else:
                    duplicates.append(url)

            # 如果有重复，更新文件
            if duplicates:
                print(f"发现 {len(duplicates)} 个重复URL，正在去除...")
                for dup in duplicates:
                    print(f"  重复: {dup}")

                # 写回去重后的URL
                with open(self.url_file, 'w', encoding='utf-8') as f:
                    for url in unique_urls:
                        f.write(f"{url}\n")

                print(f"✅ 去重完成，保留 {len(unique_urls)} 个唯一URL")
            else:
                print(f"✅ 未发现重复URL，共 {len(unique_urls)} 个URL")

    def get_all_pending_urls(self):
        """获取所有待处理的URL - 集成智能过滤"""
        if not Path(self.url_file).exists():
            print(f"错误: {self.url_file} 文件不存在")
            return []

        # 读取所有URL
        with open(self.url_file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]

        if not urls:
            print("URL文件为空")
            return []

        # 读取已处理的域名（成功和失败的）
        processed_domains = set()

        # 从success.txt读取成功的域名
        if Path(self.success_file).exists():
            with open(self.success_file, 'r', encoding='utf-8') as f:
                for line in f:
                    article_url = line.strip()
                    if article_url:
                        domain = urlparse(article_url).netloc
                        processed_domains.add(domain)

        # 从error.txt读取失败的域名
        if Path(self.error_file).exists():
            with open(self.error_file, 'r', encoding='utf-8') as f:
                for line in f:
                    error_url = line.strip()
                    if error_url:
                        domain = urlparse(error_url).netloc
                        processed_domains.add(domain)

        # 找到所有未处理的URL，并进行智能过滤
        pending_urls = []
        blocked_count = 0
        directory_count = 0

        for url in urls:
            domain = urlparse(url).netloc

            # 跳过已处理的域名
            if domain in processed_domains:
                continue

            # 跳过被阻止的域名（如果启用）
            if self.enable_blocked_domain_filter and self.is_domain_blocked(url):
                blocked_count += 1
                continue

            # 跳过目录网站（如果启用）
            if self.enable_directory_detection and self.is_directory_site(url):
                directory_count += 1
                continue

            pending_urls.append(url)

        if blocked_count > 0:
            print(f"🚫 跳过 {blocked_count} 个被阻止域名的URL")
        if directory_count > 0:
            print(f"📁 跳过 {directory_count} 个目录网站URL")

        return pending_urls

    def run(self):
        """运行主程序 - 集成智能过滤和统计"""
        print("🚀 评论机器人启动 (智能过滤版本)")
        print("=" * 60)

        # 显示配置信息
        print(f"📊 配置信息:")
        print(f"   失败阈值: {self.failure_threshold}")
        print(f"   目录网站检测: {'启用' if self.enable_directory_detection else '禁用'}")
        print(f"   被阻止域名过滤: {'启用' if self.enable_blocked_domain_filter else '禁用'}")
        print(f"   自动清理被阻止域名: {'启用' if self.auto_cleanup_blocked_domains else '禁用'}")
        print("-" * 60)

        # 第一步：检查并去除重复URL
        print("📋 检查URL重复...")
        self.deduplicate_urls()
        print("-" * 60)

        # 第二步：获取所有待处理的URL（集成智能过滤）
        print("🔍 获取待处理URL（智能过滤中）...")
        pending_urls = self.get_all_pending_urls()

        if not pending_urls:
            print("✅ 所有URL已处理完成！")
            # 显示域名统计报告
            print("\n" + self.get_domain_stats_report())
            return

        print(f"📝 待处理URL数量: {len(pending_urls)}")
        print("🚀 开始并行处理...")
        print("-" * 60)

        # 使用配置的线程数并行处理
        thread_count = self.config.get('performance', {}).get('threads', 5)
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            executor.map(self.process_single_url, pending_urls)

        print("=" * 60)
        print("✅ 所有URL处理完成！")

        # 显示最终统计报告
        print("\n📊 最终统计报告:")
        print(self.get_domain_stats_report())


if __name__ == "__main__":
    bot = CommentBot()
    bot.run()
